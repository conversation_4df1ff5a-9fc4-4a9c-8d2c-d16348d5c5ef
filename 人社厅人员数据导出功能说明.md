# 人社厅人员数据导出功能说明

## 功能概述

本功能实现了查询所有人社厅人员数据，并根据 `strUnitName`（单位名称）分组，为每个单位生成独立的Excel文件，最后将所有Excel文件打包成压缩包供下载。

## 技术实现

### 1. 新增文件

#### 1.1 导出DTO类
- **文件路径**: `hndxoa-userorg/src/main/java/com/ctsi/ssdc/entity/dto/BizHrsUserInfoExportDTO.java`
- **功能**: 定义Excel导出的数据结构，使用EasyExcel注解标注列名

#### 1.2 测试类
- **文件路径**: `hndxoa-userorg/src/test/java/com/ctsi/ssdc/service/BizHrsUserInfoServiceTest.java`
- **功能**: 提供单元测试方法验证导出功能

### 2. 修改文件

#### 2.1 Service接口
- **文件路径**: `hndxoa-userorg/src/main/java/com/ctsi/ssdc/service/IBizHrsUserInfoService.java`
- **新增方法**: `Boolean exportAllUsersByUnit(HttpServletResponse response)`

#### 2.2 Service实现类
- **文件路径**: `hndxoa-userorg/src/main/java/com/ctsi/ssdc/service/impl/BizHrsUserInfoServiceImpl.java`
- **新增方法**: 
  - `exportAllUsersByUnit()`: 主要导出逻辑
  - `convertToExportDTO()`: 数据转换方法

#### 2.3 Controller类
- **文件路径**: `hndxoa-userorg/src/main/java/com/ctsi/ssdc/controller/BizHrsUserInfoController.java`
- **新增接口**: `GET /api/bizHrsUserInfo/exportAllUsersByUnit`

## API接口说明

### 导出接口

**请求方式**: GET  
**请求路径**: `/api/bizHrsUserInfo/exportAllUsersByUnit`  
**请求参数**: 无  
**响应类型**: 文件下载（ZIP压缩包）  

**响应说明**:
- 成功时返回ZIP压缩包，包含按单位分组的Excel文件
- 失败时返回JSON错误信息

**示例请求**:
```bash
curl -X GET "http://localhost:8080/api/bizHrsUserInfo/exportAllUsersByUnit" \
     -H "Accept: application/octet-stream" \
     --output "人社厅人员数据.zip"
```

## 实现逻辑

### 1. 数据查询与分组
```java
// 查询所有人社厅人员数据
List<BizHrsUserInfo> allUsers = bizHrsUserInfoMapper.selectList(null);

// 按单位名称分组
Map<String, List<BizHrsUserInfo>> usersByUnit = allUsers.stream()
    .filter(user -> StringUtils.isNotBlank(user.getStrUnitName()))
    .collect(Collectors.groupingBy(BizHrsUserInfo::getStrUnitName));
```

### 2. Excel文件生成
```java
// 为每个单位生成Excel文件
for (Map.Entry<String, List<BizHrsUserInfo>> entry : usersByUnit.entrySet()) {
    String unitName = entry.getKey();
    List<BizHrsUserInfo> unitUsers = entry.getValue();
    
    // 转换为导出DTO
    List<BizHrsUserInfoExportDTO> exportData = unitUsers.stream()
        .map(this::convertToExportDTO)
        .collect(Collectors.toList());
    
    // 生成安全的文件名
    String safeFileName = unitName.replaceAll("[\\\\/:*?\"<>|]", "_");
    String excelFilePath = tempDir + File.separator + safeFileName + ".xlsx";
    
    // 写入Excel文件
    EasyExcel.write(excelFilePath, BizHrsUserInfoExportDTO.class)
        .sheet(unitName)
        .doWrite(exportData);
}
```

### 3. 压缩打包
```java
// 打包成ZIP文件
String zipFileName = "人社厅人员数据_" + System.currentTimeMillis() + ".zip";
FileNameUtil.setDownloadResponseHeader(response, zipFileName);

try (OutputStream outputStream = response.getOutputStream()) {
    ZipFileUtil.toZip(tempFiles, outputStream);
    outputStream.flush();
}
```

## Excel文件结构

每个Excel文件包含以下列：
- **姓名** (strUserId)
- **身份证号** (idCardNo)  
- **手机号** (strMobile)
- **单位名称** (strUnitName)
- **单位统一社会信用码** (creditCode)

## 错误处理

1. **数据为空**: 如果没有找到人社厅人员数据，返回失败状态
2. **单位数据无效**: 如果没有有效的单位数据，返回失败状态
3. **文件操作异常**: 捕获IO异常并记录日志，返回失败状态
4. **临时文件清理**: 使用finally块确保临时文件被正确清理

## 性能考虑

1. **内存优化**: 使用流式处理避免一次性加载大量数据到内存
2. **文件清理**: 及时清理临时文件避免磁盘空间占用
3. **异常处理**: 完善的异常处理机制确保系统稳定性

## 日志记录

- 记录每个Excel文件的生成情况
- 记录最终导出的单位数量
- 记录异常信息便于问题排查

## 测试建议

1. 运行单元测试验证基本功能
2. 测试大数据量场景的性能表现
3. 测试异常情况的处理逻辑
4. 验证生成的Excel文件格式和内容正确性
