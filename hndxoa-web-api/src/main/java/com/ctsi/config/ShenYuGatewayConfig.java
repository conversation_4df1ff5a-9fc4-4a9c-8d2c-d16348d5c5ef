package com.ctsi.config;

import org.apache.shenyu.springboot.starter.plugin.divide.DividePluginConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * ShenYu网关客户端配置类
 * 通过配置文件中的 shenyu.gateway.enabled 属性控制是否启用ShenYu网关客户端
 *
 * 使用方法：
 * 1. 启用网关：在配置文件中设置 shenyu.gateway.enabled=true
 * 2. 禁用网关：在配置文件中设置 shenyu.gateway.enabled=false 或删除该配置
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Configuration
@ConditionalOnProperty(
    name = "shenyu.gateway.enabled",
    havingValue = "true",
    matchIfMissing = false  // 默认不启用，必须显式设置为true才启用
)
@Import(DividePluginConfiguration.class)  // 只有当enabled=true时才导入ShenYu配置
public class ShenYuGatewayConfig {

    /**
     * 当shenyu.gateway.enabled=true时，自动导入ShenYu相关配置
     * 这里可以添加ShenYu相关的Bean配置
     */

    // 如果需要自定义ShenYu配置，可以在这里添加@Bean方法

}
