package com.ctsi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 网关配置属性类
 * 用于读取配置文件中的网关开关配置
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Component
@ConfigurationProperties(prefix = "shenyu.gateway")
public class GatewayProperties {
    
    /**
     * 是否启用网关
     * true: 启用ShenYu网关客户端
     * false: 禁用ShenYu网关客户端
     */
    private boolean enabled = false;
    
    /**
     * 网关服务器地址
     */
    private String serverUrl;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 上下文路径
     */
    private String contextPath;
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
    
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public String getContextPath() {
        return contextPath;
    }
    
    public void setContextPath(String contextPath) {
        this.contextPath = contextPath;
    }
    
    @Override
    public String toString() {
        return "GatewayProperties{" +
                "enabled=" + enabled +
                ", serverUrl='" + serverUrl + '\'' +
                ", appName='" + appName + '\'' +
                ", contextPath='" + contextPath + '\'' +
                '}';
    }
}
