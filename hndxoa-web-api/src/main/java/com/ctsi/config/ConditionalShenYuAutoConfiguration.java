package com.ctsi.config;

import org.springframework.boot.autoconfigure.AutoConfigurationImportFilter;
import org.springframework.boot.autoconfigure.AutoConfigurationMetadata;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 条件性ShenYu自动配置过滤器
 * 根据配置文件中的 shenyu.gateway.enabled 属性决定是否加载ShenYu自动配置
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Component
public class ConditionalShenYuAutoConfiguration implements AutoConfigurationImportFilter {

    @Override
    public boolean[] match(String[] autoConfigurationClasses, AutoConfigurationMetadata autoConfigurationMetadata) {
        boolean[] matches = new boolean[autoConfigurationClasses.length];
        
        for (int i = 0; i < autoConfigurationClasses.length; i++) {
            String className = autoConfigurationClasses[i];
            
            // 检查是否是ShenYu相关的自动配置类
            if (isShenYuAutoConfiguration(className)) {
                // 根据配置决定是否加载
                matches[i] = isShenYuEnabled();
            } else {
                // 非ShenYu配置类，正常加载
                matches[i] = true;
            }
        }
        
        return matches;
    }
    
    /**
     * 判断是否是ShenYu相关的自动配置类
     */
    private boolean isShenYuAutoConfiguration(String className) {
        return className != null && (
            className.contains("org.apache.shenyu") ||
            className.contains("shenyu")
        );
    }
    
    /**
     * 检查是否启用ShenYu
     */
    private boolean isShenYuEnabled() {
        // 这里可以通过Environment读取配置，但在这个阶段Environment可能还没有完全初始化
        // 所以我们使用系统属性作为备选方案
        String enabled = System.getProperty("shenyu.gateway.enabled", "false");
        return "true".equalsIgnoreCase(enabled);
    }
}
