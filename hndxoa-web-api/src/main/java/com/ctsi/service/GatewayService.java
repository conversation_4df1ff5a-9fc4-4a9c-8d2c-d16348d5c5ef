package com.ctsi.service;

import com.ctsi.config.GatewayProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 网关服务类
 * 用于管理网关的启用/禁用状态和相关操作
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Service
@ConditionalOnProperty(name = "shenyu.gateway.enabled", havingValue = "true")
public class GatewayService {
    
    private static final Logger logger = LoggerFactory.getLogger(GatewayService.class);
    
    @Autowired
    private GatewayProperties gatewayProperties;
    
    @PostConstruct
    public void init() {
        if (gatewayProperties.isEnabled()) {
            logger.info("=== ShenYu网关客户端已启用 ===");
            logger.info("应用名称: {}", gatewayProperties.getAppName());
            logger.info("上下文路径: {}", gatewayProperties.getContextPath());
            logger.info("服务器地址: {}", gatewayProperties.getServerUrl());
        } else {
            logger.info("=== ShenYu网关客户端已禁用 ===");
        }
    }
    
    /**
     * 检查网关是否启用
     * @return true=启用, false=禁用
     */
    public boolean isGatewayEnabled() {
        return gatewayProperties.isEnabled();
    }
    
    /**
     * 获取网关配置信息
     * @return 网关配置属性
     */
    public GatewayProperties getGatewayProperties() {
        return gatewayProperties;
    }
    
    /**
     * 记录网关状态日志
     */
    public void logGatewayStatus() {
        if (isGatewayEnabled()) {
            logger.info("当前网关状态: 已启用");
        } else {
            logger.info("当前网关状态: 已禁用");
        }
    }
}
