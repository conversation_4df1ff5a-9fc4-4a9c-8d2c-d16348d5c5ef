package com.ctsi;

import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.westone.WestoneClient;
import com.ctsi.manager.CloseListener;
import com.ctsi.manager.SpringBeanFactoryUtils;
import com.ctsi.manager.StartListener;
import com.ctsi.ssdc.model.SystemLogOperation;
import com.ctsi.ssdc.repository.SystemLogOperationMapper;
import com.ctsi.ssdc.util.DefaultProfileUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;

@ServletComponentScan
@SpringBootApplication(scanBasePackages = {"com.ctsi.*"}, exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class})
@MapperScan(basePackages = {"com.ctsi.**.mapper", "com.ctsi.**.*Repository", "com.ctsi.ssdc.admin.repository", "com.ctsi.ssdc.repository"})
@EnableTransactionManagement
@EnableSwagger2
public class HndxoaApiApplication {
    private static final Logger log = LoggerFactory.getLogger(HndxoaApiApplication.class);

    private static final String SERVER_SSL_KEY_STORE = "server.ssl.key-store";

    public static void main(String[] args) throws UnknownHostException {
        long startTime = System.currentTimeMillis();
        SpringApplication app = new SpringApplication(HndxoaApiApplication.class);
        app.addListeners(new StartListener());
        app.addListeners(new CloseListener());
        DefaultProfileUtil.addDefaultProfile(app);
        Environment env = app.run(args).getEnvironment();
        String protocol = "http";
        if (env.getProperty(SERVER_SSL_KEY_STORE) != null) {
            protocol = "https";
        }
        log.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! Access URLs:\n\t" +
                        "Local: \t\t{}://localhost:{}\n\t" +
                        "External: \t{}://{}:{}\n\t" +
                        "Profile(s): \t{}\n----------------------------------------------------------",
                env.getProperty("spring.application.name"),
                protocol,
                env.getProperty("server.port"),
                protocol,
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port"),
                env.getActiveProfiles());

        Runtime.getRuntime().addShutdownHook(new Thread(){
            @Override
            public void run() {
                SystemLogOperationMapper systemLogOperationMapper = SpringBeanFactoryUtils.getBean(SystemLogOperationMapper.class);
                log.info("程序停止");
                InetAddress ip = null;
                try {
                    ip = InetAddress.getLocalHost();
                    SystemLogOperation systemLogOperation = new SystemLogOperation();
                    systemLogOperation.setOperationSource("协同办公平台");
                    systemLogOperation.setIp(ip.getHostAddress());
                    systemLogOperation.setOperationType(1);
                    systemLogOperation.setOperationResult("警告");
                    systemLogOperation.setMainBody("admin");
                    systemLogOperation.setObjectBody("服务器程序停止");
                    systemLogOperation.setCreateTime(LocalDateTime.now());
                    systemLogOperation.setCreateBy(2L);
                    systemLogOperation.setCreateName("系统管理员");
                    systemLogOperationMapper.insert(systemLogOperation);
                } catch (UnknownHostException e) {
                    throw new RuntimeException(e);
                }
            }
        });

        // TODO 根据系统动态参数配置，判断是否使用密码机
        ISysConfigService sysConfigService = SpringBeanFactoryUtils.getBean(ISysConfigService.class);
        boolean isCipherMachine = sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
        if (isCipherMachine) {
            WestoneClient.initClientPool(sysConfigService);
        }

        long endTime = System.currentTimeMillis();
        long l = endTime - startTime;
        System.out.println("应用程序启动时间: " + l / 1000);
    }

}