# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  #日志级别
  level:
    ROOT: ERROR
    com.zaxxer.hikari.HikariConfig: ERROR
    org:
      springframework:
        boot:
          autoconfigure: ERROR
  io:
    lettuce:
      core:
        protocol: ERROR
  file:
    path: /data/jigou/jar/logback

# 数据库名称
dataBaseName: TYJG

spring:
  #激活一个或多个配置
  profiles:
    include: swagger,enc

  jpa:
    hibername:
      #使用@Entity时，控制表结构生成行为，none是什么都不做
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect
  # 新增多数据源管理，表单设计-模型管理走master数据源，maven依赖名：dynamic-datasource（create：2023年2月9日）
  datasource:
    dynamic:
      primary: master # 设置默认的数据源或者数据源组
      strict: false # 严格匹配数据源, 默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.132.15:5237,10.49.132.16:5237,10.49.132.17:5237)&loginMode=1&rwSeparate=1&rwPercent=33&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: TYJG
          password: Tyjg0408db8.
          driverClassName: dm.jdbc.driver.DmDriver
        du_ban:
          url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.128.7:5237,10.49.128.8:5237,10.49.128.229:5237)&loginMode=1&rwSeparate=1&rwPercent=33&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: SWDBDB
          password: Swdbdb0408db8.
          driverClassName: dm.jdbc.driver.DmDriver
        oa:
          url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.132.12:5237,10.49.132.14:5237,10.49.132.26:5237)&loginMode=1&rwSeparate=1&rwPercent=33&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: WWOA
          password: Wwoa0408db8.
          driverClassName: dm.jdbc.driver.DmDriver
        swwwxy:
          url: jdbc:dm://SHSWJXY?SHSWJXY=(10.49.128.8:5237,10.49.128.7:5237,10.49.128.229:5237)&loginMode=1&rwSeparate=0&rwPercent=33&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: SHSWJXY
          password: Shswjxy0324db8.
          driverClassName: dm.jdbc.driver.DmDriver
        swwj:
          url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.128.7:5237,10.49.128.8:5237,10.49.128.229:5237)&loginMode=1&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: WJZD
          password: Wjzd0606db8.
          driverClassName: dm.jdbc.driver.DmDriver
        swba:
          url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.128.7:5237,10.49.128.8:5237,10.49.128.229:5237)&loginMode=1&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
          username: WJZD
          password: Wjzd0606db8.
          driverClassName: dm.jdbc.driver.DmDriver
    type: com.zaxxer.hikari.HikariDataSource
    # 设置数据库连接池，最大50，最小10
    hikari:
      maximum-pool-size: 150
      minimum-idle: 30
      connection-timeout: 3000
      idle-timeout: 60000
      max-lifetime: 1800000
      validation-timeout: 5000
      leak-detection-threshold: 60000
      register-mbeans: true
      data-source-properties:
        prepareThreshold: 5
        defaultRowPrefetch: 200
        batchMode: on
        fetchSize: 1000

  shardingsphere:
    datasource:
      names: master
      master:
        driver-class-name: dm.jdbc.driver.DmDriver
        jdbc-url: jdbc:dm://SWDB_DM?SWDB_DM=(10.49.132.15:5237,10.49.132.16:5237,10.49.132.17:5237)&loginMode=1&rwSeparate=1&rwPercent=33&useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level
        username: TYJG
        password: Tyjg0408db8.
        type: com.zaxxer.hikari.HikariDataSource
        hikari:
          maximum-pool-size: 200
          minimum-idle: 30
    sharding:
      tables:
        t_sync_user_histroy_record:
          actual-data-nodes: master.t_sync_user_histroy_record_${202504..202512}
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
              range-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: id
        t_sync_org_histroy_record:
          actual-data-nodes: master.t_sync_org_histroy_record_${202504..202512}
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
              range-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: id
      default-data-source-name: master

    # 开发环境打印执行的SQL
    props:
      sql:
        show: true

  # 上传文件的大小配置，从文件模块中移到此处
  servlet:
    multipart:
      max-file-size: 2000MB
      max-request-size: 2000MB

  #redis的基础配置 *************** 8379 password: Xtbg_CS_2020
  redis:
    host: ************
    # 在家通过vpn访问的请使用下面的VPn
    #    host: localhost
    #    host: 127.0.0.1
    port: 6379
    #    port: 6379
    database: 2
    password: P@ssw0rd!Qw3rTy#zXc
    #password: powersi@redis

    jedis:
      pool:
        max-idle: 6
        max-active: 32
        max-wait: 100
        min-idle: 4

  mvc:
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp
  #spring.mvc.view.prefix=/WEB-INF/jsp/
  #spring.mvc.view.suffix=.jsp


  activiti:
    async-executor-activate: false
    asyncExecutorEnabled: false
    database-schema-update: false
    check-process-definitions: false
    history-level: none
    # 达梦数据库配置的时候需要制定为oracle
    data-base-type: oracle


server:
  #端口号
  port: 9003
  #  ssl:
  #    key-store: classpath:tomcat.jks #类路径下的自签证书
  #    key-store-password: 123123 #证书密码
  #    key-store-type: JKS # 证书类型
  jsp:
    init-parameters:
      development: true


pagehelper:
  #分页设置
  helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  pageSizeZero: true

management:
  health:
    redis:
      #关闭REDIS健康检查
      enabled: false
    elasticsearch:
      #关闭REDIS健康检查
      enabled: false


mybatis-plus:
  config-location: classpath:/mybatis-config.xml
  mapper-locations: classpath*:/mapper/*Repository.xml,classpath*:/mapper/*Mapper.xml
  global-config:
    db-config:
      #配置全局逻辑删除，指定实体类的逻辑删除字段
      logic-delete-value: 1
      logic-not-delete-value: 0
      #配置主键id生成策略采用雪花算法
      id-type: assign_id

  type-enums-package: com.ctsi.hndx.enums



#  上传文件的参数，从文件模块中移到此处
formFile:
  #配置正文的相关参数
  document:
    # 可上传个数
    limit: 1
    # 支持的类型
    extend: doc,docx,pdf,ofd,wps
    #附件配置的相关参数
  annex:
    # 可上传的个数
    limit: 10
    # 可支持的图片类型
    picExtend: jpeg,png,gif,bmp
    # 可支持的文件类型
    # fileExtend: pdf,doc,docx,xls,xlsx,ppt,pptx,htm,html,ofd
    fileExtend: doc,dot,wps,wpt,wocx,docx,docm,dotm,rtf,uot,mht,mhtml,html,xml,xls,xlt,et,xlsx,xltx,xlsm,pptx,ppt,pot,pps,ppsx,dps,dpt,pptm,potm,ppsm,pdf,zip,ofd,jpg,txt,png,gif,jpeg,bmp,svg,c,java,asp,bat,zip,rar
  #1 minIO  2 oss  3 本地存储
  storageType: 3
  # 1 使用wps的预览和转换 2 使用比翼的预览服务器 3 使用新版wps的预览和转换
  previewType: 2
  localStoragePath: /data/tyjgfile/tyjg/

# 配置奥连的安全参数
nisc:
  # 绝对路径 common.se的绝对路径，
  commonse-path: /common.se

  #前端首页地址，用来重定向到首页
  web-home: http://**************:30933/home
  # 绝对路径 param.asm
  paramasn-path: /param.asn


ctsi:
  #统计认证组件路径配置
  component-statics:
    #日志存放路径
    log-path: /data/jigou/jar/logs
    #证书路径
    cert-path: classpath:/license.lc
  workTime: 08:30,12:00|13:30,17:30
  log:
    #是否启用操作日志
    operation-log:
      enable: true
    #是否启用登录日志
    login-log:
      enable: true
  password-check:
    #密码过期时间，单位 月
    expire-months: 3
    #密码最小长度
    check-min-length: 8
    #密码最大长度
    check-max-length: 20
    #检查密码包含小写字母
    check-lowercase: true
    #检查密码包含大写字母
    check-uppercase: true
    #检查密码包含数字
    check-digit: true
    #检查密码包含键盘相邻字符
    check-keyboard: true
    #检查密码包含特殊字符
    check-special-character: true
  login:
    #密码输错达到次数，锁定用户
    bad-password-attempts: 5
    #锁定时间，单位s
    lockout-time: 3600
    #记录密码错误缓存实现，提供两种实现，redis和基于guava的缓存，redis支持集群部署，guava只支持单机使用。可选值：guavaLoginCache，redisLoginCache
    cache: redisLoginCache

  async:
    core-pool-size: 2
    max-pool-size: 50
    queue-capacity: 10000
    # By default CORS is disabled. Uncomment to enable.
    #cors:
    #allowed-origins: "*"
    #allowed-methods: "*"
    #allowed-headers: "*"
    #exposed-headers: "Authorization,Link,X-Total-Count"
    #allow-credentials: true
    #max-age: 1800
    swagger:
      default-include-pattern: /api/.*
      title: ctsi API
      description: ctsi API documentation
      version: 0.0.1
      terms-of-service-url:
      contact-name:
      contact-url:
      contact-email:
      license:
      license-url:
    systemconfig:
      webfiles: /Users/<USER>/allSelf/server/webfiles

    jwtfilter:
      enable: false

    userService: userServiceImpl
    sqlService: sqlServiceImpl
    metaDataService: metaDataServiceImpl
    menuService: menuServiceImpl
    bigDataService:
  # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  #允许跨域，多个以逗号分隔
  corspaths: "/api/**"

  security:
    authentication:
      jwt:
        #token key密钥
        secret: my-secret-token-to-change-in-production
        #token失效时间，秒
        token-validity-in-seconds: 28800
        #rememberme token失效时间
        token-validity-in-seconds-for-remember-me: 2592000
  captcha:
    #验证码KEY策略
    keyProvider: simpleCaptchaKeyProvider
    #验证码组件缓存实现，提供两种实现，redis和基于guava的缓存，redis支持集群部署，guava只支持单机使用。可选值：guavaCaptchaCache，redisCaptchaCache
    cache: redisCaptchaCache

cform:
  # 暂时没用
  formSecretKey: DalY#xP7D5Q
  chargeservicevul: http://localhost:${server.port}
  #数据库的名称,biyi-form组件使用
  datasourceTableSchema: ${dataBaseName}

# 短信的配置方式，默认采用的方式
sms:
  smssendclassname:
  #短信验证码有效时间 单位：秒
  validTime: 90
  #短信操作频繁 单位：秒
  sendSmsTime: 60

redis:
  # 1 使用RedisTemplate(默认), 2 使用CtgJedisPool
  type: 1
  HostAndPort:
    - **************:31134
    - **************:31134
  database: group.2610001_162_HN_QSXTBG_CACHE_001.xtbg
  password: HN_QSXTBG_CACHE_001#mwsW%SdTeG0A
  period: 3000
  monitorTimeout: 200
  MaxTotal: 10
  MaxIdle: 10
  MinIdle: 3
  MaxWaitMillis: 3000

elasticsearch:
  host: ************
  port: 9200
  schema: http
  username: elastic
  password: 5kmiEwp9H9fceb2MkEQ6

# rocketmq配置  http://***************:8888  控制台可视化
rocketmq:
  #多个用,隔开
  name-server: **************:9876
  producer:
    #多个用,隔开
    group: swoa
  consumer:
    group: swoa

#卫士通配置
wst:
  randomUrl: http://127.0.0.1:80
  verifyUrl: http://127.0.0.1:80
  appNo: 123456
  logReportUrl: http://127.0.0.1:80

rabbitmq:
  host: ************
  port: 5672
  username: admin
  password: jO0ID1F!JR2h^wCw
  virtual-host: /
  lz-routingKey: zwylz

# Apache ShenYu 网关客户端配置
shenyu:
  # 生产环境网关开关配置 - 根据实际需要设置
  gateway:
    enabled: false  # 生产环境默认禁用，需要时改为true
  register:
    registerType: http
    serverLists: http://your-shenyu-admin-server:9095  # 请修改为实际的ShenYu Admin服务地址
    props:
      username: admin
      password: 123456
  client:
    http:
      props:
        contextPath: /api
        appName: hndxoa-api-prod
        port: 9003
        isFull: false
