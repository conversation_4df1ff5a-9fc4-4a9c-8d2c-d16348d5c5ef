# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  #日志级别
  level:
    ROOT: debug
    com.zaxxer.hikari.HikariConfig: info
    org:
      springframework:
        boot:
          autoconfigure: info
    io.lettuce.core: INFO

  file:
    path: D:\file\

# 数据库名称
dataBaseName: HN_SWOA

spring:
  #激活一个或多个配置
  profiles:
    include: swagger,enc

  jpa:
    hibername:
      #使用@Entity时，控制表结构生成行为，none是什么都不做
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect

  # 新增多数据源管理，普通表使用dynamic-datasource，分表使用shardingsphere
  datasource:
    dynamic:
      primary: master #设置默认的数据源，默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/HN_SWOA?compatibleMode=mysql&useUnicode=true&characterEncoding=utf8
          username: HN_SWOA
          password: Hn_swoa0326db39.
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 200
            minimum-idle: 30
        duban:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/swwwdb?useUnicode=true&characterEncoding=utf8
          username: SWWWDB
          password: Aa147369..
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
        oa:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/SWOA?useUnicode=true&characterEncoding=utf8
          username: SWOA
          password: Swoa0328db39.
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
        swwwxy:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/SWWWXY?useUnicode=true&characterEncoding=utf8
          username: SWWWXY
          password: Swwwxy0403db39.
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
        swba:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/SWBA?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true&clobAsString=true&compatibleMode=mysql
          username: SWBA
          password: ceshi_SWBA0506db39.
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
        swwj:
          driver-class-name: dm.jdbc.driver.DmDriver
          url: jdbc:dm://10.50.0.39:5237/SWWJ?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true&clobAsString=true&compatibleMode=mysql
          username: SWWJ
          password: ceshi_SWWJ04018db39.
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10

  shardingsphere:
    datasource:
      names: master
      master:
        driver-class-name: dm.jdbc.driver.DmDriver
        jdbc-url: jdbc:dm://10.50.0.39:5237/HN_SWOA?compatibleMode=mysql&useUnicode=true&characterEncoding=utf8
        username: HN_SWOA
        password: Hn_swoa0326db39.
        type: com.zaxxer.hikari.HikariDataSource
        hikari:
          maximum-pool-size: 200
          minimum-idle: 30
    sharding:
      tables:
        t_sync_user_histroy_record:
          actual-data-nodes: master.t_sync_user_histroy_record_${202504..202512}
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
              range-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: id
        t_sync_org_histroy_record:
          actual-data-nodes: master.t_sync_org_histroy_record_${202504..202512}
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
              range-algorithm-class-name: com.ctsi.config.dmSupport.MonthTableShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: id
      default-data-source-name: master
    props:
      sql:
        show: true
        # 关键配置：启用查询带别名功能
        query-with-alias: true
#      # 关键优化：禁用启动时的元数据加载
#      check:
#        table:
#          metadata:
#            enabled: false
#      # 设置最大连接数，减少并发查询
#      max:
#        connections:
#          size:
#            per:
#              query: 1
#      # 禁用SQL解析缓存以减少内存占用
#        parse:
#          cache:
#            enabled: false
#      # 设置执行器大小
#      executor:
#        size: 4

  # 上传文件的大小配置，从文件模块中移到此处
  servlet:
    multipart:
      max-file-size: 2000MB
      max-request-size: 2000MB

  redis:
    host: **********
    port: 6379
    database: 103
    password: 4W8P$wyE8*l@RVSeBCOUUXdRSR

    jedis:
      pool:
        max-idle: 6
        max-active: 32
        max-wait: 100
        min-idle: 4
  cache:
      redis:
        cache-names:
          userCache-findByCurrentUserName: 24h
          userCache-getUserByUsernameOrMobile: 24h
  mvc:
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp
  #spring.mvc.view.prefix=/WEB-INF/jsp/
  #spring.mvc.view.suffix=.jsp


  activiti:
    async-executor-activate: false
    asyncExecutorEnabled: false
    database-schema-update: false
    check-process-definitions: false
    history-level: none
    # 达梦数据库配置的时候需要制定为oracle
    data-base-type: oracle


server:
  #端口号
  port: 9003
  #  ssl:
  #    key-store: classpath:tomcat.jks #类路径下的自签证书
  #    key-store-password: 123123 #证书密码
  #    key-store-type: JKS # 证书类型
  jsp:
    init-parameters:
      development: true


pagehelper:
  #分页设置
  helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  pageSizeZero: true

management:
  health:
    redis:
      #关闭REDIS健康检查
      enabled: false
    elasticsearch:
      #关闭REDIS健康检查
      enabled: false
    db:
      #关闭数据库健康检查
      enabled: false


mybatis-plus:
  config-location: classpath:/mybatis-config.xml
  mapper-locations: classpath*:/mapper/*Repository.xml,classpath*:/mapper/*Mapper.xml
  global-config:
    db-config:
      #配置全局逻辑删除，指定实体类的逻辑删除字段
      logic-delete-value: 1
      logic-not-delete-value: 0
      #配置主键id生成策略采用雪花算法
      id-type: assign_id

  type-enums-package: com.ctsi.hndx.enums



#  上传文件的参数，从文件模块中移到此处
formFile:
  #配置正文的相关参数
  document:
    # 可上传个数
    limit: 1
    # 支持的类型
    extend: doc,docx,pdf,ofd,wps
    #附件配置的相关参数
  annex:
    # 可上传的个数
    limit: 10
    # 可支持的图片类型
    picExtend: jpeg,png,gif,bmp
    # 可支持的文件类型
    # fileExtend: pdf,doc,docx,xls,xlsx,ppt,pptx,htm,html,ofd
    fileExtend: doc,dot,wps,wpt,wocx,docx,docm,dotm,rtf,uot,mht,mhtml,html,xml,xls,xlt,et,xlsx,xltx,xlsm,pptx,ppt,pot,pps,ppsx,dps,dpt,pptm,potm,ppsm,pdf,zip,ofd,jpg,txt,png,gif,jpeg,bmp,svg,c,java,asp,bat,zip,rar
  #1 minIO  2 oss  3 本地存储
  storageType: 3
  # 1 使用wps的预览和转换 2 使用比翼的预览服务器 3 使用新版wps的预览和转换
  previewType: 2
  localStoragePath: /opt/disk/dxsz/oa/

# 配置奥连的安全参数
nisc:
  # 绝对路径 common.se的绝对路径，
  commonse-path: /common.se

  #前端首页地址，用来重定向到首页
  web-home: http://**************:30933/home
  # 绝对路径 param.asm
  paramasn-path: /param.asn


ctsi:
  #统计认证组件路径配置
  component-statics:
    #日志存放路径
    log-path: /opt/disk/dxsz/logs
    #证书路径
    cert-path: classpath:/license.lc
  workTime: 08:30,12:00|13:30,17:30
  log:
    #是否启用操作日志
    operation-log:
      enable: true
    #是否启用登录日志
    login-log:
      enable: true
  password-check:
    #密码过期时间，单位 月
    expire-months: 3
    #密码最小长度
    check-min-length: 8
    #密码最大长度
    check-max-length: 20
    #检查密码包含小写字母
    check-lowercase: true
    #检查密码包含大写字母
    check-uppercase: true
    #检查密码包含数字
    check-digit: true
    #检查密码包含键盘相邻字符
    check-keyboard: true
    #检查密码包含特殊字符
    check-special-character: true
  login:
    #密码输错达到次数，锁定用户
    bad-password-attempts: 5
    #锁定时间，单位s
    lockout-time: 3600
    #记录密码错误缓存实现，提供两种实现，redis和基于guava的缓存，redis支持集群部署，guava只支持单机使用。可选值：guavaLoginCache，redisLoginCache
    cache: redisLoginCache

  async:
    core-pool-size: 10
    max-pool-size: 50
    queue-capacity: 10000
    # By default CORS is disabled. Uncomment to enable.
    #cors:
    #allowed-origins: "*"
    #allowed-methods: "*"
    #allowed-headers: "*"
    #exposed-headers: "Authorization,Link,X-Total-Count"
    #allow-credentials: true
    #max-age: 1800
    swagger:
      default-include-pattern: /api/.*
      title: ctsi API
      description: ctsi API documentation
      version: 0.0.1
      terms-of-service-url:
      contact-name:
      contact-url:
      contact-email:
      license:
      license-url:
    systemconfig:
      webfiles: /Users/<USER>/allSelf/server/webfiles

    jwtfilter:
      enable: false

    userService: userServiceImpl
    sqlService: sqlServiceImpl
    metaDataService: metaDataServiceImpl
    menuService: menuServiceImpl
    bigDataService:
  # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  #允许跨域，多个以逗号分隔
  corspaths: "/api/**"

  security:
    authentication:
      jwt:
        #token key密钥
        secret: my-secret-token-to-change-in-production
        #token失效时间，秒
        token-validity-in-seconds: 28800
        #rememberme token失效时间
        token-validity-in-seconds-for-remember-me: 2592000
  captcha:
    #验证码KEY策略
    keyProvider: simpleCaptchaKeyProvider
    #验证码组件缓存实现，提供两种实现，redis和基于guava的缓存，redis支持集群部署，guava只支持单机使用。可选值：guavaCaptchaCache，redisCaptchaCache
    cache: redisCaptchaCache

cform:
  # 暂时没用
  formSecretKey: DalY#xP7D5Q
  chargeservicevul: http://localhost:${server.port}
  #数据库的名称,biyi-form组件使用
  datasourceTableSchema: ${dataBaseName}

# 短信的配置方式，默认采用的方式
sms:
  smssendclassname:
  #短信验证码有效时间 单位：秒
  validTime: 90
  #短信操作频繁 单位：秒
  sendSmsTime: 60

redis:
  # 1 使用RedisTemplate(默认), 2 使用CtgJedisPool
  type: 1
  HostAndPort:
    - **************:31134
    - **************:31134
  database: group.2610001_162_HN_QSXTBG_CACHE_001.xtbg
  password: HN_QSXTBG_CACHE_001#mwsW%SdTeG0A
  period: 3000
  monitorTimeout: 200
  MaxTotal: 10
  MaxIdle: 10
  MinIdle: 3
  MaxWaitMillis: 3000

elasticsearch:
  host: ***************
  port: 9200
  schema: http
  username: elastic
  password: on6gCfVIyvf8TzBQRjb7

rabbitmq:
  host: **********
  port: 5672
  username: shuzizhengfu
  password: GW5LhwuG69P9a6QPR@z0
  virtual-host: /
  lz-routingKey: zwylz
#  控制台 http://**********:15672/

#卫士通配置
wst:
  randomUrl: http://127.0.0.1:80
  verifyUrl: http://127.0.0.1:80
  appNo: 123456
  logReportUrl: http://127.0.0.1:80
defservice:
  ip: **************
  port: 32700


# Apache ShenYu 网关客户端配置 - 方案4：最小化配置
shenyu:
  register:
    registerType: http
    serverLists: http://localhost:9095
    props:
      username: admin
      password: 123456
  client:
    http:
      props:
        contextPath: /api
        appName: hndxoa-api
        port: 9003
        isFull: false
