package com.ctsi.swoa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 领导分组用户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
//@Getter
//@Setter
//@TableName("SW_LEADER_GROUP_USER")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("SW_LEADER_GROUP_USER")
@ApiModel(value = "SwLeaderGroupUser对象", description = "领导分组用户")
public class SwLeaderGroupUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组id")
    @NotNull(message = "分组id不能为空")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long groupId;

    @ApiModelProperty("绑定领导id")
    @NotNull(message = "绑定领导id不能为空")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long bindUserId;

    @ApiModelProperty("用户排序")
    private int userSort;
}
