<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.schedule.mapper.TLeaderScheduleMapper">

    <resultMap id="BaseResultMap" type="com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="leader_id" jdbcType="BIGINT" property="leaderId"/>
        <result column="leader_name" jdbcType="VARCHAR" property="leaderName"/>
        <result column="activity_content" jdbcType="VARCHAR" property="activityContent"/>
        <result column="activity_place" jdbcType="VARCHAR" property="activityPlace"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="time_period" jdbcType="INTEGER" property="timePeriod"/>
        <result column="other_leaders" jdbcType="VARCHAR" property="otherLeaders"/>
        <result column="cater_company" jdbcType="VARCHAR" property="caterCompany"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
    </resultMap>

    <select id = "getLeaderWeekSchedule" parameterType = "com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO"
     resultMap = "BaseResultMap">
        SELECT id, leader_id, leader_name, activity_content, activity_place, start_time, end_time,
        time_period, other_leaders, cater_company, contact_name, mobile, remark, department_id, company_id, tenant_id, create_by, create_name
        FROM t_leader_schedule
        <where>
            <if test = "dto.leaderId != null">
                and leader_id = #{dto.leaderId}
            </if>
            <if test = "dto.leaderStopTime != null">
                and date_format(create_time, '%Y-%m-%d %H:%i:%s') &lt;= date_format(#{dto.leaderStopTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "dto.startTime != null">
                and date_format(start_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{dto.startTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "dto.endTime != null">
                and date_format(end_time, '%Y-%m-%d %H:%i:%s') &lt;= date_format(#{dto.endTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "null != dto.leaderIdList and dto.leaderIdList.size > 0">
                and leader_id in
                <foreach collection = "dto.leaderIdList" item = "item" open = "(" separator = "," close = ")">
                    #{item}
                </foreach>
            </if>
            and deleted = 0
            ORDER BY start_time
        </where>
    </select>

    <select id = "getYesterdaySchedule" resultMap = "BaseResultMap">
        SELECT id, leader_id, leader_name, activity_content, activity_place, start_time, end_time, create_time,
        time_period, other_leaders, cater_company, contact_name, mobile, remark, department_id, company_id, tenant_id
        FROM t_leader_schedule
        <where>
            and TO_DAYS(NOW()) - TO_DAYS(start_time) = 1
            and deleted = 0
        </where>
    </select>

</mapper>
