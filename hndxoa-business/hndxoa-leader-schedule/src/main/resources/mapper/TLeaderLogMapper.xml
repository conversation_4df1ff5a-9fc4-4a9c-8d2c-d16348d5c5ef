<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.schedule.mapper.TLeaderLogMapper">
    <resultMap id="BaseResultMap" type="com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="leader_id" jdbcType="BIGINT" property="leaderId"/>
        <result column="leader_name" jdbcType="VARCHAR" property="leaderName"/>
        <result column="log_content" jdbcType="VARCHAR" property="logContent"/>
        <result column="makeup_time" jdbcType="TIMESTAMP" property="makeupTime"/>
        <result column="time_period" jdbcType="INTEGER" property="timePeriod"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
    </resultMap>

    <select id = "getLeaderWeekLog" parameterType = "com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO"
     resultMap = "BaseResultMap">
        SELECT id, leader_id, leader_name, log_content, makeup_time, time_period, record_type, start_time, end_time, create_by, create_name
        FROM t_leader_log
        <where>
            <if test = "dto.leaderId != null">
                and leader_id = #{dto.leaderId}
            </if>
            <if test = "dto.leaderStopTime != null">
                and date_format(create_time, '%Y-%m-%d %H:%i:%s') &lt;= date_format(#{dto.leaderStopTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "dto.startTime != null">
                and date_format(makeup_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{dto.startTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "dto.endTime != null">
                and date_format(makeup_time, '%Y-%m-%d %H:%i:%s') &lt;= date_format(#{dto.endTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test = "null != dto.leaderIdList and dto.leaderIdList.size > 0">
                and leader_id in
                <foreach collection = "dto.leaderIdList" item = "item" open = "(" separator = "," close = ")">
                    #{item}
                </foreach>
            </if>
            and deleted = 0
            order by makeup_time
        </where>
    </select>

</mapper>
