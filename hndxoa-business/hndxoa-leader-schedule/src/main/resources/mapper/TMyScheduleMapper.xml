<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.schedule.mapper.TMyScheduleMapper">

    <resultMap id="BaseResultMap" type="com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="schedule_content" jdbcType="VARCHAR" property="scheduleContent"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="important_signs" jdbcType="VARCHAR" property="importantSigns"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>

    <select id = "queryMyScheduleList" parameterType = "com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO"
     resultMap = "BaseResultMap">
        SELECT id, schedule_content, start_time, end_time, important_signs, department_id, company_id, tenant_id
        FROM t_my_schedule
        <where>
            deleted = 0
            <if test = "dto.createBy != null">
                and create_by = #{dto.createBy}
            </if>
            <if test = "dto.startTime != null">
                <choose>
                    <when test="dto.databaseType == 'kingbase8'"> -- 人大金仓数据库
                        and TO_CHAR(start_time,'%Y-%m-%d %H:%i:%s') &gt;= TO_CHAR(#{dto.startTime},'%Y-%m-%d %H:%i:%s')
                    </when>
                    <otherwise>
                        and date_format(start_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{dto.startTime},'%Y-%m-%d %H:%i:%s')
                    </otherwise>
                </choose>
            </if>
            <if test = "dto.endTime != null">
                <choose>
                    <when test="dto.databaseType == 'kingbase8'"> -- 人大金仓数据库
                        and TO_CHAR(end_time, '%Y-%m-%d %H:%i:%s') &lt;= TO_CHAR(#{dto.endTime},'%Y-%m-%d %H:%i:%s')
                    </when>
                    <otherwise>
                        and date_format(end_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{dto.endTime},'%Y-%m-%d %H:%i:%s')
                    </otherwise>
                </choose>

            </if>
        </where>
    </select>
</mapper>
