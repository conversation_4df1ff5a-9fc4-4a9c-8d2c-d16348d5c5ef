<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.schedule.mapper.TLeaderEntrustMapper">

    <select id="queryPageLeaderEntrustInUnit" parameterType="com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO"
            resultType="com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO">
        SELECT DISTINCT tle.id, tle.entrusted_id, tle.entrusted_name, tle.leader_id, tle.leader_name, tlm.group_by leader_in_group, tle.tenant_id,tle.entrusted_id, tsdr.sorted, tlm.sort
        FROM t_leader_entrust tle
        INNER JOIN t_leader_management tlm ON tle.leader_id = tlm.id
        INNER JOIN t_sys_dict_record tsdr ON tlm.group_by = tsdr.code
        WHERE tle.deleted = 0
        AND tlm.deleted = 0
        AND tsdr.deleted = 0
        <if test="dto.leaderName != null and dto.leaderName != ''">
            AND tle.leader_name LIKE concat('%',#{dto.leaderName},'%')
        </if>
        <if test = "null != dto.dictRecordIdList and dto.dictRecordIdList.size > 0">
            AND tsdr.id IN
            <foreach collection="dto.dictRecordIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY tle.entrusted_id, tsdr.sorted, tlm.sort, tle.id
    </select>

</mapper>
