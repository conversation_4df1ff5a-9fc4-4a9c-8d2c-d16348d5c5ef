<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.schedule.mapper.TLeaderManagementMapper">

    <select id="queryPageLeaderInUnit" parameterType="com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO"
        resultType="com.ctsi.hndx.schedule.entity.TLeaderManagement">
        SELECT tlm.id, tlm.user_id, tlm.leader_name, tlm.sort, tlm.enable_time, tlm.stop_time, tlm.group_by, tlm.status, tlm.tenant_id
        FROM t_leader_management tlm
        INNER JOIN t_sys_dict_record tsdr
        ON tlm.group_by = tsdr.code
        where tlm.deleted = 0
        AND tsdr.deleted = 0
        <if test="dto.leaderName != null and dto.leaderName != ''">
            AND tlm.leader_name like concat('%',#{dto.leaderName},'%')
        </if>
        <if test="dto.status != null">
            <choose>
                <when test="dto.status == 1">
                    AND tlm.enable_time &lt;= NOW() AND tlm.stop_time &gt;= NOW()
                </when>
                <otherwise>
                    AND (tlm.enable_time IS NULL
                        OR tlm.stop_time IS NULL
                        OR tlm.enable_time &gt; NOW()
                        OR stop_time &lt; NOW()
                    )
                </otherwise>
            </choose>
        </if>
        <if test = "null != dto.dictRecordIdList and dto.dictRecordIdList.size > 0">
            AND tsdr.id IN
            <foreach collection="dto.dictRecordIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY tsdr.sorted, tlm.sort
    </select>
</mapper>
