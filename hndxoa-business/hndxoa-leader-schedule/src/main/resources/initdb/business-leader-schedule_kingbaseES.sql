CREATE TABLE "public"."t_leader_entrust" (
                                             "id" int8 NOT NULL,
                                             "entrusted_id" int8 NULL,
                                             "entrusted_name" varchar(32 char) NULL,
	"leader_id" int8 NULL,
	"leader_name" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "t_leader_entrust_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_leader_entrust"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."t_leader_entrust"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_leader_entrust"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."t_leader_entrust"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_leader_entrust"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_leader_entrust"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "public"."t_leader_entrust"."leader_id" IS '领导ID(领导表主键)';
COMMENT ON COLUMN "public"."t_leader_entrust"."entrusted_name" IS '被委托人名称';
COMMENT ON COLUMN "public"."t_leader_entrust"."entrusted_id" IS '被委托人ID';
COMMENT ON COLUMN "public"."t_leader_entrust"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_leader_entrust" IS '领导委托信息表';


CREATE TABLE "public"."t_leader_log" (
                                         "id" int8 NOT NULL,
                                         "leader_id" int8 NULL,
                                         "leader_name" varchar(32 char) NULL,
	"log_content" varchar(512 char) NULL,
	"makeup_time" timestamp(6) NULL,
	"time_period" int4 NULL,
	"record_type" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"start_time" timestamp(6) NULL,
	"end_time" timestamp(6) NULL,
	CONSTRAINT "t_leader_log_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_leader_log"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."t_leader_log"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."t_leader_log"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."t_leader_log"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_leader_log"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_leader_log"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_leader_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_leader_log"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."t_leader_log"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."t_leader_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_leader_log"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_leader_log"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_leader_log"."record_type" IS '录入方式(1:补录 2:自动生成)';
COMMENT ON COLUMN "public"."t_leader_log"."time_period" IS '时间段(上午、下午、全天)';
COMMENT ON COLUMN "public"."t_leader_log"."makeup_time" IS '补录时间(不能大于当前日期)';
COMMENT ON COLUMN "public"."t_leader_log"."log_content" IS '日志内容';
COMMENT ON COLUMN "public"."t_leader_log"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "public"."t_leader_log"."leader_id" IS '领导ID(领导表主键ID)';
COMMENT ON COLUMN "public"."t_leader_log"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_leader_log" IS '领导日志表';


CREATE TABLE "public"."t_leader_management" (
                                                "id" int8 NOT NULL,
                                                "user_id" int8 NULL,
                                                "leader_name" varchar(32 char) NULL,
	"sort" int4 NULL,
	"enable_time" timestamp(6) NULL,
	"stop_time" timestamp(6) NULL,
	"group_by" varchar(32 char) NULL,
	"status" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "t_leader_management_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_leader_management"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."t_leader_management"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_leader_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."t_leader_management"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_leader_management"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."t_leader_management"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_leader_management"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_leader_management"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."t_leader_management"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."t_leader_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_leader_management"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_leader_management"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_leader_management"."status" IS '状态(暂时没有使用)';
COMMENT ON COLUMN "public"."t_leader_management"."group_by" IS '分组';
COMMENT ON COLUMN "public"."t_leader_management"."stop_time" IS '停用日期';
COMMENT ON COLUMN "public"."t_leader_management"."enable_time" IS '启用日期';
COMMENT ON COLUMN "public"."t_leader_management"."sort" IS '排序(不能录入同一排序号)';
COMMENT ON COLUMN "public"."t_leader_management"."leader_name" IS '领导姓名(不能录入同一领导姓名)';
COMMENT ON COLUMN "public"."t_leader_management"."user_id" IS '关联用户表的用户ID';
COMMENT ON COLUMN "public"."t_leader_management"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_leader_management" IS '领导管理表';


CREATE TABLE "public"."t_leader_schedule" (
                                              "id" int8 NOT NULL,
                                              "leader_id" int8 NULL,
                                              "leader_name" varchar(32 char) NULL,
	"activity_content" varchar(512 char) NULL,
	"activity_place" varchar(255 char) NULL,
	"start_time" timestamp(6) NULL,
	"end_time" timestamp(6) NULL,
	"time_period" int4 NULL,
	"other_leaders" varchar(255 char) NULL,
	"cater_company" varchar(64 char) NULL,
	"contact_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"remark" varchar(255 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "t_leader_schedule_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_leader_schedule"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."t_leader_schedule"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_leader_schedule"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_leader_schedule"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_leader_schedule"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_leader_schedule"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."t_leader_schedule"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."t_leader_schedule"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_leader_schedule"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_leader_schedule"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_leader_schedule"."remark" IS '备注';
COMMENT ON COLUMN "public"."t_leader_schedule"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."t_leader_schedule"."contact_name" IS '联系人名称';
COMMENT ON COLUMN "public"."t_leader_schedule"."cater_company" IS '承办单位';
COMMENT ON COLUMN "public"."t_leader_schedule"."other_leaders" IS '其他出席领导';
COMMENT ON COLUMN "public"."t_leader_schedule"."time_period" IS '时间段(上午、下午、全天)';
COMMENT ON COLUMN "public"."t_leader_schedule"."end_time" IS '结束时间(一次只能录入一天的日程)';
COMMENT ON COLUMN "public"."t_leader_schedule"."start_time" IS '开始时间(一次只能录入一天的日程)';
COMMENT ON COLUMN "public"."t_leader_schedule"."activity_place" IS '活动地点';
COMMENT ON COLUMN "public"."t_leader_schedule"."activity_content" IS '活动内容';
COMMENT ON COLUMN "public"."t_leader_schedule"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "public"."t_leader_schedule"."leader_id" IS '领导ID(领导表主键ID)';
COMMENT ON COLUMN "public"."t_leader_schedule"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_leader_schedule" IS '领导日程表';


CREATE TABLE "public"."t_my_schedule" (
                                          "id" int8 NOT NULL,
                                          "schedule_content" varchar(255 char) NULL,
	"start_time" timestamp(6) NULL,
	"end_time" timestamp(6) NULL,
	"important_signs" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "t_my_schedule_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_my_schedule"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."t_my_schedule"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_my_schedule"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_my_schedule"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_my_schedule"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_my_schedule"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."t_my_schedule"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."t_my_schedule"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_my_schedule"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_my_schedule"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_my_schedule"."important_signs" IS '重要标志';
COMMENT ON COLUMN "public"."t_my_schedule"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."t_my_schedule"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."t_my_schedule"."schedule_content" IS '日程内容';
COMMENT ON COLUMN "public"."t_my_schedule"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_my_schedule" IS '我的日程信息表';
