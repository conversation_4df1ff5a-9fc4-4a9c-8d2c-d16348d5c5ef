CREATE TABLE `t_leader_entrust` (
                                    `id` bigint NOT NULL COMMENT '主键ID',
                                    `entrusted_id` bigint DEFAULT NULL COMMENT '被委托人ID',
                                    `entrusted_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '被委托人名称',
                                    `leader_id` bigint DEFAULT NULL COMMENT '领导ID(领导表主键)',
                                    `leader_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导姓名',
                                    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                    `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                    `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                    `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                    `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                    `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='领导委托信息表';

CREATE TABLE `t_leader_log` (
                                `id` bigint NOT NULL COMMENT '主键ID',
                                `leader_id` bigint DEFAULT NULL COMMENT '领导ID(领导表主键ID)',
                                `leader_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导姓名',
                                `log_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '日志内容',
                                `makeup_time` datetime DEFAULT NULL COMMENT '补录时间(不能大于当前日期)',
                                `time_period` int DEFAULT NULL COMMENT '时间段(上午、下午、全天)',
                                `record_type` int DEFAULT NULL COMMENT '录入方式(1:补录 2:自动生成)',
                                `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='领导日志表';

CREATE TABLE `t_leader_management` (
                                       `id` bigint NOT NULL COMMENT '主键ID',
                                       `user_id` bigint DEFAULT NULL COMMENT '关联用户表的用户ID',
                                       `leader_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导姓名(不能录入同一领导姓名)',
                                       `sort` int DEFAULT NULL COMMENT '排序(不能录入同一排序号)',
                                       `enable_time` datetime DEFAULT NULL COMMENT '启用日期',
                                       `stop_time` datetime DEFAULT NULL COMMENT '停用日期',
                                       `group_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分组',
                                       `status` int DEFAULT NULL COMMENT '状态(暂时没有使用)',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                       `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                       `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                       `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
                                       `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                       `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                       `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                       `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='领导管理表';

CREATE TABLE `t_leader_schedule` (
                                     `id` bigint NOT NULL COMMENT '主键ID',
                                     `leader_id` bigint DEFAULT NULL COMMENT '领导ID(领导表主键ID)',
                                     `leader_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导姓名',
                                     `activity_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '活动内容',
                                     `activity_place` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '活动地点',
                                     `start_time` datetime DEFAULT NULL COMMENT '开始时间(一次只能录入一天的日程)',
                                     `end_time` datetime DEFAULT NULL COMMENT '结束时间(一次只能录入一天的日程)',
                                     `time_period` int DEFAULT NULL COMMENT '时间段(上午、下午、全天)',
                                     `other_leaders` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '其他出席领导',
                                     `cater_company` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '承办单位',
                                     `contact_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系人名称',
                                     `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
                                     `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                     `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                     `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                     `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                     `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                     `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                     `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='领导日程表';

CREATE TABLE `t_my_schedule` (
                                 `id` bigint NOT NULL COMMENT '主键ID',
                                 `schedule_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '日程内容',
                                 `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                 `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                 `important_signs` varchar(32) DEFAULT NULL COMMENT '重要标志',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                 `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                 `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                 `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                 `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='我的日程信息表';

