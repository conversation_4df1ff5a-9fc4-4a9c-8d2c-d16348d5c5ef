CREATE TABLE "myapp"."t_leader_entrust"
(
"id" BIGINT NOT NULL,
"entrusted_id" BIGINT,
"entrusted_name" VARCHAR(32),
"leader_id" BIGINT,
"leader_name" VARCHAR(32),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_leader_entrust" IS '领导委托信息表';COMMENT ON COLUMN "myapp"."t_leader_entrust"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."entrusted_id" IS '被委托人ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."entrusted_name" IS '被委托人名称';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."leader_id" IS '领导ID(领导表主键)';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_leader_entrust"."deleted" IS '逻辑删除';




CREATE TABLE "myapp"."t_leader_log"
(
"id" BIGINT NOT NULL,
"leader_id" BIGINT,
"leader_name" VARCHAR(32),
"log_content" VARCHAR(512),
"makeup_time" TIMESTAMP(0),
"time_period" INT,
"record_type" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
"start_time" TIMESTAMP(0),
"end_time" TIMESTAMP(0, ),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_leader_log" IS '领导日志表';COMMENT ON COLUMN "myapp"."t_leader_log"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."leader_id" IS '领导ID(领导表主键ID)';
COMMENT ON COLUMN "myapp"."t_leader_log"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "myapp"."t_leader_log"."log_content" IS '日志内容';
COMMENT ON COLUMN "myapp"."t_leader_log"."makeup_time" IS '补录时间(不能大于当前日期)';
COMMENT ON COLUMN "myapp"."t_leader_log"."time_period" IS '时间段(上午、下午、全天)';
COMMENT ON COLUMN "myapp"."t_leader_log"."record_type" IS '录入方式(1:补录 2:自动生成)';
COMMENT ON COLUMN "myapp"."t_leader_log"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_leader_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_leader_log"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."t_leader_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_leader_log"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_leader_log"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "myapp"."t_leader_log"."start_time" IS '开始时间';
COMMENT ON COLUMN "myapp"."t_leader_log"."end_time" IS '结束时间';




CREATE TABLE "myapp"."t_leader_management"
(
"id" BIGINT NOT NULL,
"user_id" BIGINT,
"leader_name" VARCHAR(32),
"sort" INT,
"enable_time" TIMESTAMP(0),
"stop_time" TIMESTAMP(0),
"group_by" VARCHAR(32),
"status" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"department_name" VARCHAR(32),
"company_id" BIGINT,
"company_name" VARCHAR(32),
"tenant_id, " BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_leader_management" IS '领导管理表';COMMENT ON COLUMN "myapp"."t_leader_management"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."user_id" IS '关联用户表的用户ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."leader_name" IS '领导姓名(不能录入同一领导姓名)';
COMMENT ON COLUMN "myapp"."t_leader_management"."sort" IS '排序(不能录入同一排序号)';
COMMENT ON COLUMN "myapp"."t_leader_management"."enable_time" IS '启用日期';
COMMENT ON COLUMN "myapp"."t_leader_management"."stop_time" IS '停用日期';
COMMENT ON COLUMN "myapp"."t_leader_management"."group_by" IS '分组';
COMMENT ON COLUMN "myapp"."t_leader_management"."status" IS '状态(暂时没有使用)';
COMMENT ON COLUMN "myapp"."t_leader_management"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_leader_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_leader_management"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."t_leader_management"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_leader_management"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."department_name" IS '部门名称';
COMMENT ON COLUMN "myapp"."t_leader_management"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."t_leader_management"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_leader_management"."deleted" IS '逻辑删除';




CREATE TABLE "myapp"."t_leader_schedule"
(
"id" BIGINT NOT NULL,
"leader_id" BIGINT,
"leader_name" VARCHAR(32),
"activity_content" VARCHAR(512),
"activity_place" VARCHAR(255),
"start_time" TIMESTAMP(0),
"end_time" TIMESTAMP(0),
"time_period" INT,
"other_leaders" VARCHAR(255),
"cater_company" VARCHAR(64),
"contact_name" VARCHAR(32),
"mobile" VARCHAR(32),
"remark" VARCHAR(255),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update, _name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_leader_schedule" IS '领导日程表';COMMENT ON COLUMN "myapp"."t_leader_schedule"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."leader_id" IS '领导ID(领导表主键ID)';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."leader_name" IS '领导姓名';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."activity_content" IS '活动内容';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."activity_place" IS '活动地点';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."start_time" IS '开始时间(一次只能录入一天的日程)';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."end_time" IS '结束时间(一次只能录入一天的日程)';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."time_period" IS '时间段(上午、下午、全天)';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."other_leaders" IS '其他出席领导';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."cater_company" IS '承办单位';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."contact_name" IS '联系人名称';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."mobile" IS '联系电话';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."remark" IS '备注';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_leader_schedule"."deleted" IS '逻辑删除';




CREATE TABLE "myapp"."t_my_schedule"
(
"id" BIGINT NOT NULL,
"schedule_content" VARCHAR(255),
"start_time" TIMESTAMP(0),
"end_time" TIMESTAMP(0),
"important_signs" VARCHAR(32),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_my_schedule" IS '我的日程信息表';COMMENT ON COLUMN "myapp"."t_my_schedule"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."schedule_content" IS '日程内容';
COMMENT ON COLUMN "myapp"."t_my_schedule"."start_time" IS '开始时间';
COMMENT ON COLUMN "myapp"."t_my_schedule"."end_time" IS '结束时间';
COMMENT ON COLUMN "myapp"."t_my_schedule"."important_signs" IS '重要标志';
COMMENT ON COLUMN "myapp"."t_my_schedule"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_my_schedule"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_my_schedule"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."t_my_schedule"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_my_schedule"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_my_schedule"."deleted" IS '逻辑删除';




