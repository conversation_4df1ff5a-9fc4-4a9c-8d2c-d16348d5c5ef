package com.ctsi.hndx.schedule.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.schedule.entity.TLeaderLog;
import com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 领导日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ITLeaderLogService extends SysBaseServiceI<TLeaderLog> {


    /**
     * 分页查询
     */
    PageResult<TLeaderLogDTO> queryListPage(TLeaderLogDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TLeaderLogDTO> queryList(TLeaderLogDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TLeaderLogDTO findOne(Long id);

    /**
     * 新增
     */
    TLeaderLogDTO create(TLeaderLogDTO entity);


    /**
     * 更新
     */
    int update(TLeaderLogDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTLeaderLogId
     */
    boolean existByTLeaderLogId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TLeaderLogDTO> dataList);

    /**
     * 日程转换为日志
     * @param tLeaderScheduleDTOList
     * @return
     */
    Boolean scheduleToLog(List<TLeaderScheduleDTO> tLeaderScheduleDTOList);


    /**
     * 获取时间范围内受委托人关联领导的日志信息
     * @param tLeaderLogDTO
     * @return
     */
    List<TLeaderManagementDTO> queryLogListByTime(TLeaderLogDTO tLeaderLogDTO);

    /**
     * 分页查询领导日志(身份为领导)
     * @param tLeaderLogDTO
     * @param basePageForm
     * @return
     */
    PageResult<TLeaderLogDTO> queryLogPageByLeader(TLeaderLogDTO tLeaderLogDTO, BasePageForm basePageForm);

    /**
     * 分页查询领导日志(身份为联络人)
     * @param tLeaderLogDTO
     * @param basePageForm
     * @return
     */
    PageResult<TLeaderLogDTO> queryLogPageByLiaison(TLeaderLogDTO tLeaderLogDTO, BasePageForm basePageForm);


    /**
     * 查询领导日志(身份为领导)
     * @param tLeaderLogDTO
     * @return
     */
    List<TLeaderManagementDTO> queryLogListByLeader(TLeaderLogDTO tLeaderLogDTO);

    /**
     * 查询领导日志(身份为联络人)
     * @param tLeaderLogDTO
     * @return
     */
    List<TLeaderManagementDTO> queryLogListByLiaison(TLeaderLogDTO tLeaderLogDTO);

}
