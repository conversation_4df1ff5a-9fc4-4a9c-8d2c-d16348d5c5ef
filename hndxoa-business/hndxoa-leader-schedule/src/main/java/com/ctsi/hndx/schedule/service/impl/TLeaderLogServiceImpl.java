package com.ctsi.hndx.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.schedule.entity.TLeaderLog;
import com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.hndx.schedule.mapper.TLeaderLogMapper;
import com.ctsi.hndx.schedule.service.ITLeaderEntrustService;
import com.ctsi.hndx.schedule.service.ITLeaderLogService;
import com.ctsi.hndx.schedule.service.ITLeaderManagementService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 领导日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */

@Slf4j
@Service
public class TLeaderLogServiceImpl extends SysBaseServiceImpl<TLeaderLogMapper, TLeaderLog> implements ITLeaderLogService {

    @Autowired
    private TLeaderLogMapper tLeaderLogMapper;

    @Autowired
    private ITLeaderManagementService itLeaderManagementService;

    @Autowired
    private ITLeaderEntrustService itLeaderEntrustService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderLogDTO> queryListPage(TLeaderLogDTO entityDTO, BasePageForm basePageForm) {
        // 判断当前登录用户ID是否存在
        if (Objects.isNull(entityDTO.getUserId())) {
            Long loginId = SecurityUtils.getCurrentUserId();
            entityDTO.setUserId(loginId);
        }

        // 如果登录的人是领导，则查询其所在分组的所有日志信息
        if (itLeaderManagementService.existAllByUserId(entityDTO.getUserId())) {
            return this.queryLogPageByLeader(entityDTO, basePageForm);
        }

        // 如果登录的认识被委托人，则查询其委托领导的日志信息
        else if (itLeaderEntrustService.isConsigneeInAllLeaderEntrustRel(entityDTO.getUserId())) {
            return this.queryLogPageByLiaison(entityDTO, basePageForm);
        }

        // 如果既不是领导又被委托人，则直接返回
        else {
            return new PageResult<>();
        }

    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLeaderLogDTO> queryList(TLeaderLogDTO entityDTO) {
        LambdaQueryWrapper<TLeaderLog> queryWrapper = new LambdaQueryWrapper();
        List<TLeaderLog> listData = tLeaderLogMapper.selectList(queryWrapper);
        List<TLeaderLogDTO> TLeaderLogDTOList = ListCopyUtil.copy(listData, TLeaderLogDTO.class);
        return TLeaderLogDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLeaderLogDTO findOne(Long id) {
        TLeaderLog  tLeaderLog =  tLeaderLogMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLeaderLog,TLeaderLogDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLeaderLogDTO create(TLeaderLogDTO entityDTO) {
        if (Objects.isNull(entityDTO.getMakeupTime())) {
            entityDTO.setMakeupTime(LocalDateTime.now());
        }
        if (Objects.isNull(entityDTO.getRecordType())) {
            entityDTO.setRecordType(1);
        }
        TLeaderLog tLeaderLog =  BeanConvertUtils.copyProperties(entityDTO,TLeaderLog.class);
        save(tLeaderLog);
        return  BeanConvertUtils.copyProperties(tLeaderLog,TLeaderLogDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLeaderLogDTO entity) {
        TLeaderLog tLeaderLog = BeanConvertUtils.copyProperties(entity,TLeaderLog.class);
        return tLeaderLogMapper.updateById(tLeaderLog);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLeaderLogMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLeaderLogId
     * @return
     */
    @Override
    public boolean existByTLeaderLogId(Long TLeaderLogId) {
        if (TLeaderLogId != null) {
            LambdaQueryWrapper<TLeaderLog> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLeaderLog::getId, TLeaderLogId);
            List<TLeaderLog> result = tLeaderLogMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLeaderLogDTO> dataList) {
        List<TLeaderLog> result = ListCopyUtil.copy(dataList, TLeaderLog.class);
        return saveBatch(result);
    }


    /**
     * 日程转换为日志
     * @param tLeaderScheduleDTOList
     * @return
     */
    @Override
    public Boolean scheduleToLog(List<TLeaderScheduleDTO> tLeaderScheduleDTOList) {
        List<TLeaderLog> leaderLogList = new ArrayList<>();
        for (TLeaderScheduleDTO tLeaderScheduleDTO : tLeaderScheduleDTOList) {
            TLeaderLog leaderLog = new TLeaderLog();
            // 日志赋值
            BeanUtils.copyProperties(tLeaderScheduleDTO, leaderLog);
            leaderLog.setId(null);
            // 补录时间设置成日程活动开始时间
            leaderLog.setMakeupTime(tLeaderScheduleDTO.getStartTime());
            leaderLog.setLogContent(tLeaderScheduleDTO.getActivityContent());
            leaderLog.setRecordType(2);
            leaderLogList.add(leaderLog);
        }
        saveBatch(leaderLogList);
        return true;
    }

    /**
     * 获取时间范围内受委托人关联领导的日志信息
     * @param tLeaderLogDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryLogListByTime(TLeaderLogDTO tLeaderLogDTO) {
        if (Objects.isNull(tLeaderLogDTO.getUserId())) {
            throw new BusinessException("userId不允许为空");
        }

        // 如果登录的人是领导，则查询其所在分组的所有日志信息
        if (itLeaderManagementService.existAllByUserId(tLeaderLogDTO.getUserId())) {
            return this.queryLogListByLeader(tLeaderLogDTO);
        }

        // 如果登录的人为被委托人，则查询其委托领导的日志信息
        else if (itLeaderEntrustService.isConsigneeInAllLeaderEntrustRel(tLeaderLogDTO.getUserId())) {
            return this.queryLogListByLiaison(tLeaderLogDTO);
        }

        // 如果既不是领导又被委托人，则直接返回
        else {
            return new ArrayList<>();
        }
    }


    /**
     * 分页查询领导日志(身份为领导)
     * @param tLeaderLogDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderLogDTO> queryLogPageByLeader(TLeaderLogDTO tLeaderLogDTO, BasePageForm basePageForm) {

        int andFlag = 0;
        List<TLeaderManagementDTO> leaderManagementDTOList = itLeaderManagementService.getAllLeadersInGroup(
                tLeaderLogDTO.getUserId());
        if (CollectionUtils.isEmpty(leaderManagementDTOList)) {
            return new PageResult<>();
        }

        LambdaQueryWrapper<TLeaderLog> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotBlank(tLeaderLogDTO.getLeaderName())) {
            queryWrapper.like(TLeaderLog::getLeaderName, tLeaderLogDTO.getLeaderName());
        }

        // 需要按组区分数据
        Map<String, List<TLeaderManagementDTO>> groupLeaderMap = leaderManagementDTOList.stream()
                .collect(Collectors.groupingBy(TLeaderManagementDTO::getGroupBy));

        // 按组处理数据
        for (Map.Entry<String, List<TLeaderManagementDTO>> map : groupLeaderMap.entrySet()) {
            // 获取该领导在该组的信息
            TLeaderManagementDTO tLeaderManagementDTO = map.getValue().stream().filter(i ->
                    tLeaderLogDTO.getUserId().equals(i.getUserId())).collect(Collectors.toList()).get(0);

            // 判断该领导在该组是否激活
            if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(tLeaderManagementDTO.getEnableTime())) {
                continue;
            }
            List<Long> leaderIdList = map.getValue().stream().map(i -> i.getId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaderIdList)) {
                continue;
            }

            if (andFlag == 0) {
                // 判断该领导是否被禁用
                if (nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                    queryWrapper.and(wq-> wq.in(TLeaderLog::getLeaderId, leaderIdList));
                } else {
                    queryWrapper.and(wq-> wq.in(TLeaderLog::getLeaderId, leaderIdList)
                            .le(TLeaderLog::getCreateTime, tLeaderManagementDTO.getStopTime()));
                }
            } else {
                // 判断该领导是否被禁用
                if (nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                    queryWrapper.or().and(wq-> wq.in(TLeaderLog::getLeaderId, leaderIdList));
                } else {
                    queryWrapper.or().and(wq-> wq.in(TLeaderLog::getLeaderId, leaderIdList)
                            .le(TLeaderLog::getCreateTime, tLeaderManagementDTO.getStopTime()));
                }
            }

            andFlag = 1;
        }
        queryWrapper.orderByDesc(TLeaderLog::getMakeupTime);
        IPage<TLeaderLog> pageData = tLeaderLogMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //返回
        IPage<TLeaderLogDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLeaderLogDTO.class));

        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 分页查询领导日志(身份为联络人)
     * @param tLeaderLogDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderLogDTO> queryLogPageByLiaison(TLeaderLogDTO tLeaderLogDTO, BasePageForm basePageForm) {

        List<TLeaderManagementDTO> leaderAndConsigneeWeekScheduleList = itLeaderManagementService.getAllLeadersByConsigneeId(
                tLeaderLogDTO.getUserId());

        List<Long> leaderIdList = leaderAndConsigneeWeekScheduleList.stream()
                .map(x -> x.getId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderIdList)) {
            return new PageResult<>();
        }

        LambdaQueryWrapper<TLeaderLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TLeaderLog::getLeaderId, leaderIdList);
        if (StringUtils.isNotBlank(tLeaderLogDTO.getLeaderName())) {
            queryWrapper.like(TLeaderLog::getLeaderName, tLeaderLogDTO.getLeaderName());
        }
        queryWrapper.orderByDesc(TLeaderLog::getMakeupTime);
        IPage<TLeaderLog> pageData = tLeaderLogMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //返回
        IPage<TLeaderLogDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLeaderLogDTO.class));

        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());

    }


    /**
     * 查询领导日志(身份为领导)
     * @param tLeaderLogDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryLogListByLeader(TLeaderLogDTO tLeaderLogDTO) {

        List<TLeaderManagementDTO> leaderAndConsigneeWeekLogList = new ArrayList<>();

        List<TLeaderManagementDTO> leaderManagementDTOList = itLeaderManagementService.getAllLeadersInGroup(tLeaderLogDTO.getUserId());

        // 需要按组区分数据
        Map<String, List<TLeaderManagementDTO>> groupLeaderMap = leaderManagementDTOList.stream()
                .collect(Collectors.groupingBy(TLeaderManagementDTO::getGroupBy));

        // 按组处理数据
        for (Map.Entry<String, List<TLeaderManagementDTO>> map : groupLeaderMap.entrySet()) {
            // 获取该领导在该组的信息
            TLeaderManagementDTO tLeaderManagementDTO = map.getValue().stream().filter(i ->
                    tLeaderLogDTO.getUserId().equals(i.getUserId())).collect(Collectors.toList()).get(0);

            // 判断该领导在该组是否激活
            if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(tLeaderManagementDTO.getEnableTime())) {
                continue;
            }

            for (TLeaderManagementDTO leader : map.getValue()) {
                // 查询该领导在时间范围内的所有日志
                TLeaderLogDTO dto = new TLeaderLogDTO();
                dto.setLeaderId(leader.getId());
                if (nowTime.isAfter(tLeaderManagementDTO.getStopTime())) {
                    dto.setLeaderStopTime(tLeaderManagementDTO.getStopTime());
                }
                dto.setStartTime(tLeaderLogDTO.getStartTime());
                dto.setEndTime(tLeaderLogDTO.getEndTime());
                List<TLeaderLogDTO> leaderWeekLogList = tLeaderLogMapper.getLeaderWeekLog(dto);
                leader.setTLeaderLogDTOList(leaderWeekLogList);
                leaderAndConsigneeWeekLogList.add(leader);
            }
        }
        return leaderAndConsigneeWeekLogList;
    }


    /**
     * 查询领导日志(身份为联络人)
     * @param tLeaderLogDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryLogListByLiaison(TLeaderLogDTO tLeaderLogDTO) {
        List<TLeaderManagementDTO> leaderAndConsigneeWeekLogList = itLeaderManagementService.getAllLeadersByConsigneeId(
                tLeaderLogDTO.getUserId());

        for (TLeaderManagementDTO leader : leaderAndConsigneeWeekLogList) {
            // 判断该领导是否激活
            if (Objects.isNull(leader.getEnableTime()) || Objects.isNull(leader.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(leader.getEnableTime())) {
                continue;
            }
            // 查询该领导在时间范围内的所有日志
            TLeaderLogDTO dto = new TLeaderLogDTO();
            dto.setLeaderId(leader.getId());
            if (nowTime.isAfter(leader.getStopTime())) {
                dto.setLeaderStopTime(leader.getStopTime());
            }
            dto.setStartTime(tLeaderLogDTO.getStartTime());
            dto.setEndTime(tLeaderLogDTO.getEndTime());
            List<TLeaderLogDTO> leaderWeekLogList = tLeaderLogMapper.getLeaderWeekLog(dto);
            leader.setTLeaderLogDTOList(leaderWeekLogList);
        }
        return leaderAndConsigneeWeekLogList;
    }
}
