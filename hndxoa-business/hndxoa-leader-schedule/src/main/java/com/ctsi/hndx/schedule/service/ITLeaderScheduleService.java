package com.ctsi.hndx.schedule.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.schedule.entity.TLeaderSchedule;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 领导日程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ITLeaderScheduleService extends SysBaseServiceI<TLeaderSchedule> {


    /**
     * 分页查询
     */
    PageResult<TLeaderScheduleDTO> queryListPage(TLeaderScheduleDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TLeaderScheduleDTO> queryList(TLeaderScheduleDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TLeaderScheduleDTO findOne(Long id);

    /**
     * 新增
     */
    TLeaderScheduleDTO create(TLeaderScheduleDTO entity);


    /**
     * 更新
     */
    int update(TLeaderScheduleDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTLeaderScheduleId
     */
    boolean existByTLeaderScheduleId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TLeaderScheduleDTO> dataList);

    /**
     * 获取前一天的所有日程
     * @return
     */
    List<TLeaderScheduleDTO> getYesterdaySchedule();

    /**
     * 获取时间范围内受委托人关联领导的日程信息
     * @param tLeaderScheduleDTO
     * @return
     */
    List<TLeaderManagementDTO> queryScheduleListByTime(TLeaderScheduleDTO tLeaderScheduleDTO);

    /**
     * 分页查询领导日程(身份为领导)
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    PageResult<TLeaderScheduleDTO> querySchedulePageByLeader(TLeaderScheduleDTO entityDTO, BasePageForm basePageForm);

    /**
     * 分页查询领导日程(身份为联络人)
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    PageResult<TLeaderScheduleDTO> querySchedulePageByLiaison(TLeaderScheduleDTO entityDTO, BasePageForm basePageForm);


    /**
     * 查询领导日程(身份为领导)
     * @param tLeaderScheduleDTO
     * @return
     */
    List<TLeaderManagementDTO> queryScheduleListByLeader(TLeaderScheduleDTO tLeaderScheduleDTO);

    /**
     * 查询领导日程(身份为联络人)
     * @param tLeaderScheduleDTO
     * @return
     */
    List<TLeaderManagementDTO> queryScheduleListByLiaison(TLeaderScheduleDTO tLeaderScheduleDTO);

    /**
     * 领导日程导出
     *
     * @param response
     * @return
     */
    Boolean exportLeaderSchedule(HttpServletResponse response);

}
