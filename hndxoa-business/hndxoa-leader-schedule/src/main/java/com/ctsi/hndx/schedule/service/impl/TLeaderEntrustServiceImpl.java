package com.ctsi.hndx.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.schedule.entity.TLeaderEntrust;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.mapper.TLeaderEntrustMapper;
import com.ctsi.hndx.schedule.service.ITLeaderEntrustService;
import com.ctsi.hndx.schedule.service.ITLeaderManagementService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 领导委托信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */

@Slf4j
@Service
public class TLeaderEntrustServiceImpl extends SysBaseServiceImpl<TLeaderEntrustMapper, TLeaderEntrust> implements ITLeaderEntrustService {

    @Autowired
    private TLeaderEntrustMapper tLeaderEntrustMapper;

    @Autowired
    private ITLeaderManagementService leaderManagementService;

    @Autowired
    private ITSysDictRecordService itSysDictRecordService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderEntrustDTO> queryListPage(TLeaderEntrustDTO entityDTO, BasePageForm basePageForm) {
        // 查询当前单位下数据字典配置的领导分组信息
        List<Long> dictRecordIdList = itSysDictRecordService.getDictRecordListByDictCode("leaderGroupBy", null)
                .stream().map(i -> i.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dictRecordIdList)) {
            return new PageResult<>();
        }
        entityDTO.setDictRecordIdList(dictRecordIdList);
        IPage<TLeaderEntrustDTO> tLeaderEntrustDTOIPage = tLeaderEntrustMapper.queryPageLeaderEntrustInUnit(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        return new PageResult<>(tLeaderEntrustDTOIPage.getRecords(), tLeaderEntrustDTOIPage.getTotal(), tLeaderEntrustDTOIPage.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLeaderEntrustDTO> queryList(TLeaderEntrustDTO entityDTO) {
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
            List<TLeaderEntrust> listData = tLeaderEntrustMapper.selectList(queryWrapper);
            List<TLeaderEntrustDTO> TLeaderEntrustDTOList = ListCopyUtil.copy(listData, TLeaderEntrustDTO.class);
        return TLeaderEntrustDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLeaderEntrustDTO findOne(Long id) {
        TLeaderEntrust tLeaderEntrust =  tLeaderEntrustMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLeaderEntrust,TLeaderEntrustDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLeaderEntrustDTO create(TLeaderEntrustDTO entityDTO) {
        log.info("TLeaderEntrustServiceImpl.create entityDTO = {}", entityDTO);
        if (Objects.isNull(entityDTO.getEntrustedId()) || Objects.isNull(entityDTO.getLeaderId())) {
            throw new BusinessException("领导ID和被委托人ID不允许为空");
        }
        // 新增领导委托前需要判断领导和被委托人是否已存在
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderEntrust::getEntrustedId, entityDTO.getEntrustedId());
        queryWrapper.eq(TLeaderEntrust::getLeaderId, entityDTO.getLeaderId());
        List<TLeaderEntrust> leaderEntrustList = tLeaderEntrustMapper.selectListOnlyAddTenantId(queryWrapper);
        if (CollectionUtils.isNotEmpty(leaderEntrustList)) {
            log.error("该领导和被委托人已存在: entrustedId = {}, leaderId = {}",
                    entityDTO.getEntrustedId(), entityDTO.getLeaderId());
            throw new BusinessException("该领导和被委托人已存在");
        }

        TLeaderEntrust tLeaderEntrust = BeanConvertUtils.copyProperties(entityDTO,TLeaderEntrust.class);
        save(tLeaderEntrust);
        return  BeanConvertUtils.copyProperties(tLeaderEntrust,TLeaderEntrustDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLeaderEntrustDTO entity) {
        TLeaderEntrust tLeaderEntrust = BeanConvertUtils.copyProperties(entity,TLeaderEntrust.class);
        return tLeaderEntrustMapper.updateById(tLeaderEntrust);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLeaderEntrustMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLeaderEntrustId
     * @return
     */
    @Override
    public boolean existByTLeaderEntrustId(Long TLeaderEntrustId) {
        if (TLeaderEntrustId != null) {
            LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLeaderEntrust::getId, TLeaderEntrustId);
            List<TLeaderEntrust> result = tLeaderEntrustMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLeaderEntrustDTO> dataList) {
        List<TLeaderEntrust> result = ListCopyUtil.copy(dataList, TLeaderEntrust.class);
        return saveBatch(result);
    }

    /**
     * 根据用户表ID判断该用户是否是被委托人
     * @param userId
     * @return
     */
    @Override
    public Boolean isConsignee(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderEntrust::getEntrustedId, userId);
        List<TLeaderEntrust> tLeaderEntrusts = tLeaderEntrustMapper.selectListOnlyAddTenantId(queryWrapper).stream().filter(i -> {
            // 需要排除掉被禁用领导关联的联络人
            if (Objects.isNull(i.getLeaderId())) {
                return false;
            }
            TLeaderManagementDTO tLeaderManagementDTO = leaderManagementService.findOne(i.getLeaderId());
            if (Objects.isNull(tLeaderManagementDTO)) {
                return false;
            }
            if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                return false;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isAfter(tLeaderManagementDTO.getEnableTime()) && nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tLeaderEntrusts)) {
            return false;
        }
        return true;
    }


    /**
     * 根据用户表ID判断该用户是否是被委托人(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public Boolean isConsigneeInAllLeaderEntrustRel(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderEntrust::getEntrustedId, userId);
        Integer count = tLeaderEntrustMapper.selectCountOnlyAddTenantId(queryWrapper);
        return count > 0;
    }

    /**
     * 根据所委托人ID查询领导委托信息(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderEntrustDTO> getLeaderEntrustByEntrustId(Long userId) {
        List<TLeaderEntrustDTO> tLeaderEntrusts = this.getAllLeaderEntrustByEntrustId(userId).stream()
                .filter(i -> {
                    if (Objects.isNull(i.getLeaderId())) {
                        return false;
                    }
                    TLeaderManagementDTO tLeaderManagementDTO = leaderManagementService.findOne(i.getLeaderId());
                    if (Objects.isNull(tLeaderManagementDTO)) {
                        return false;
                    }
                    if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                        return false;
                    }
                    LocalDateTime nowTime = LocalDateTime.now();
                    if (nowTime.isAfter(tLeaderManagementDTO.getEnableTime()) && nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                        return true;
                    }
                    return false;
                }).distinct().collect(Collectors.toList());
        return tLeaderEntrusts;
    }


    /**
     * 根据所委托人ID查询领导委托信息(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderEntrustDTO> getAllLeaderEntrustByEntrustId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderEntrust::getEntrustedId, userId);
        List<TLeaderEntrust> tLeaderEntrusts = tLeaderEntrustMapper.selectListOnlyAddTenantId(queryWrapper);
        return ListCopyUtil.copy(tLeaderEntrusts, TLeaderEntrustDTO.class);
    }


    /**
     * 删除领导委托关联关系
     * @param leaderId
     */
    @Override
    public Boolean deleteEntrustByLeaderId(Long leaderId) {
        if (Objects.isNull(leaderId)) {
            throw new BusinessException("leaderId不允许为空");
        }
        LambdaQueryWrapper<TLeaderEntrust> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderEntrust::getLeaderId, leaderId);
        List<Long> idList = tLeaderEntrustMapper.selectListOnlyAddTenantId(queryWrapper).stream()
                .map(i -> i.getId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        tLeaderEntrustMapper.deleteBatchIds(idList);
        return true;
    }

}
