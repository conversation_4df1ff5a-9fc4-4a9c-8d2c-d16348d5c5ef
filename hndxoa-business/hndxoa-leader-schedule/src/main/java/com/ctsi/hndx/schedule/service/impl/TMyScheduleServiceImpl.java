package com.ctsi.hndx.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.schedule.entity.TMySchedule;
import com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO;
import com.ctsi.hndx.schedule.mapper.TMyScheduleMapper;
import com.ctsi.hndx.schedule.service.ITMyScheduleService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.DatabaseTypeUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 我的日程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */

@Slf4j
@Service
public class TMyScheduleServiceImpl extends SysBaseServiceImpl<TMyScheduleMapper, TMySchedule> implements ITMyScheduleService {

    @Autowired
    private TMyScheduleMapper tMyScheduleMapper;

    @Autowired
    private DatabaseTypeUtils databaseTypeUtils;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TMyScheduleDTO> queryListPage(TMyScheduleDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TMySchedule> queryWrapper = new LambdaQueryWrapper();

        IPage<TMySchedule> pageData = tMyScheduleMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TMyScheduleDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TMyScheduleDTO.class));

        return new PageResult<TMyScheduleDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TMyScheduleDTO> queryList(TMyScheduleDTO entityDTO) {
        LambdaQueryWrapper<TMySchedule> queryWrapper = new LambdaQueryWrapper();
        List<TMySchedule> listData = tMyScheduleMapper.selectList(queryWrapper);
        List<TMyScheduleDTO> TMyScheduleDTOList = ListCopyUtil.copy(listData, TMyScheduleDTO.class);
        return TMyScheduleDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TMyScheduleDTO findOne(Long id) {
        TMySchedule tMySchedule = tMyScheduleMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tMySchedule, TMyScheduleDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TMyScheduleDTO create(TMyScheduleDTO entityDTO) {
        TMySchedule tMySchedule = BeanConvertUtils.copyProperties(entityDTO, TMySchedule.class);
        save(tMySchedule);
        return BeanConvertUtils.copyProperties(tMySchedule, TMyScheduleDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TMyScheduleDTO entity) {
        TMySchedule tMySchedule = BeanConvertUtils.copyProperties(entity, TMySchedule.class);
        return tMyScheduleMapper.updateById(tMySchedule);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tMyScheduleMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TMyScheduleId
     * @return
     */
    @Override
    public boolean existByTMyScheduleId(Long TMyScheduleId) {
        if (TMyScheduleId != null) {
            LambdaQueryWrapper<TMySchedule> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TMySchedule::getId, TMyScheduleId);
            List<TMySchedule> result = tMyScheduleMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TMyScheduleDTO> dataList) {
        List<TMySchedule> result = ListCopyUtil.copy(dataList, TMySchedule.class);
        return saveBatch(result);
    }

    /**
     * 查询时间范围内我的日程
     *
     * @param tMyScheduleDTO
     * @return
     */
    @Override
    public List<TMyScheduleDTO> queryTMyScheduleListByTime(TMyScheduleDTO tMyScheduleDTO) {
        tMyScheduleDTO.setDatabaseType(databaseTypeUtils.getDatabaseType().getDatabaseName());
        List<TMyScheduleDTO> myScheduleDTOList = tMyScheduleMapper.queryMyScheduleList(tMyScheduleDTO);
        return myScheduleDTOList;
    }
}
