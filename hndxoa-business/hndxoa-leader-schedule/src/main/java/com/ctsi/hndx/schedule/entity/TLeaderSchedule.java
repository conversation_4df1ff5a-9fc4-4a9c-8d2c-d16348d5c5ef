package com.ctsi.hndx.schedule.entity;

import com.ctsi.hndx.common.BaseEntity;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 领导日程表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLeaderSchedule对象", description="领导日程表")
public class TLeaderSchedule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 领导ID
     */
    @ApiModelProperty(value = "领导ID(领导表主键ID)")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    /**
     * 活动内容
     */
    @ApiModelProperty(value = "活动内容")
    private String activityContent;

    /**
     * 活动地点
     */
    @ApiModelProperty(value = "活动地点")
    private String activityPlace;

    /**
     * 开始时间(一次只能录入一天的日程)
     */
    @ApiModelProperty(value = "开始时间(一次只能录入一天的日程)")
    private LocalDateTime startTime;

    /**
     * 结束时间(一次只能录入一天的日程)
     */
    @ApiModelProperty(value = "结束时间(一次只能录入一天的日程)")
    private LocalDateTime endTime;

    /**
     * 时间段(上午、下午、全天)
     */
    @ApiModelProperty(value = "时间段(上午、下午、全天)")
    private Integer timePeriod;

    /**
     * 其他出席领导
     */
    @ApiModelProperty(value = "其他出席领导")
    private String otherLeaders;

    /**
     * 承办单位
     */
    @ApiModelProperty(value = "承办单位")
    private String caterCompany;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
