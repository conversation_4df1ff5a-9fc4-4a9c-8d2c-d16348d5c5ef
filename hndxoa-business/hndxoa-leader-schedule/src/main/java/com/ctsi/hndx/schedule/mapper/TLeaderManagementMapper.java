package com.ctsi.hndx.schedule.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.mybatisplus.sort.SortMapper;
import com.ctsi.hndx.schedule.entity.TLeaderManagement;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 领导管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface TLeaderManagementMapper extends MybatisBaseMapper<TLeaderManagement> , SortMapper {

    /**
     * 查询本单位所有领导信息
     * @param iPage
     * @param leaderManagementDTO
     * @return
     */
    IPage<TLeaderManagement> queryPageLeaderInUnit(IPage iPage, @Param("dto") TLeaderManagementDTO leaderManagementDTO);

}
