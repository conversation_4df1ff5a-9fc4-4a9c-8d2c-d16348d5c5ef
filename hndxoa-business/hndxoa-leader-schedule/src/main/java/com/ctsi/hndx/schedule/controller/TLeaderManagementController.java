package com.ctsi.hndx.schedule.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.service.ITLeaderManagementService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLeaderManagement")
@Api(value = "领导管理表", tags = "领导管理表接口")
public class TLeaderManagementController extends BaseController {

    private static final String ENTITY_NAME = "tLeaderManagement";

    @Autowired
    private ITLeaderManagementService tLeaderManagementService;



    /**
     *  新增领导管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导管理表批量数据")
    public ResultVO createBatch(@RequestBody List<TLeaderManagementDTO> tLeaderManagementList) {
       Boolean  result = tLeaderManagementService.insertBatch(tLeaderManagementList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导管理表数据")
    public ResultVO<TLeaderManagementDTO> create(@RequestBody TLeaderManagementDTO tLeaderManagementDTO)  {
        TLeaderManagementDTO result = tLeaderManagementService.create(tLeaderManagementDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新领导管理表数据")
    public ResultVO update(@RequestBody TLeaderManagementDTO tLeaderManagementDTO) {
	    Assert.notNull(tLeaderManagementDTO.getId(), "general.IdNotNull");
        int count = tLeaderManagementService.update(tLeaderManagementDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除领导管理表数据")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLeaderManagementService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    @PostMapping("/batchDelete")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "批量删除领导管理表数据")
    @ApiOperation(value = "批量删除存在数据", notes = "传入参数")
    public ResultVO delete(@RequestBody List<Long> idList) {
        boolean bool = tLeaderManagementService.batchDelete(idList);
        return ResultVO.success(bool);
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TLeaderManagementDTO tLeaderManagementDTO = tLeaderManagementService.findOne(id);
        return ResultVO.success(tLeaderManagementDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLeaderManagementPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TLeaderManagementDTO>> queryTLeaderManagementPage(TLeaderManagementDTO tLeaderManagementDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLeaderManagementService.queryListPage(tLeaderManagementDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLeaderManagement")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TLeaderManagementDTO>> queryTLeaderManagement(TLeaderManagementDTO tLeaderManagementDTO) {
       List<TLeaderManagementDTO> list = tLeaderManagementService.queryList(tLeaderManagementDTO);
       return ResultVO.success(list);
   }

    /**
     * 根据用户ID判断该用户是否是领导管理表中的领导
     */
    @GetMapping("/existByUserId/{userId}")
    @ApiOperation(value = "根据用户ID判断该用户是否是领导管理表中的领导", notes = "传入参数")
    public ResultVO existByUserId(@PathVariable Long userId) {
        Boolean bool = tLeaderManagementService.existByUserId(userId);
        return ResultVO.success(bool);
    }

    /**
     * 根据领导ID启用停用该领导
     */
    @PostMapping("/setLeaderStatus")
    @ApiOperation(value = "根据领导ID启用停用该领导", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "根据领导ID启用停用该领导")
    public ResultVO setLeaderStatusByLeaderId(@RequestBody TLeaderManagementDTO tLeaderManagementDTO) {
        Boolean bool = tLeaderManagementService.setLeaderStatus(tLeaderManagementDTO);
        return ResultVO.success(bool);
    }

    @GetMapping("/getLeadersByConsigneeId")
    @ApiOperation(value = "根据登录用户ID查询其委托的领导信息", notes = "传入参数")
    public ResultVO<List<TLeaderManagementDTO>> getLeadersByConsigneeId(TLeaderManagementDTO tLeaderManagementDTO) {
        tLeaderManagementDTO.setLoginId(SecurityUtils.getCurrentUserId());
        List<TLeaderManagementDTO> list = tLeaderManagementService.getLeadersByConsigneeId(tLeaderManagementDTO);
        return ResultVO.success(list);
    }

    /**
     * 获取排序号并判断是否存在重复（前端加排序用）
     * @param sort
     * @return
     */
    @GetMapping("/getLeaderSort")
    @ApiOperation(value = "获取排序号并判断是否存在重复")
    public ResultVO<Boolean> getLeaderSort(Integer sort, String groupBy){
        Boolean result = tLeaderManagementService.getLeaderSort(sort, groupBy);
        return ResultVO.success(result);
    }

}
