package com.ctsi.hndx.schedule.entity;

import com.ctsi.hndx.common.BaseEntity;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 领导管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLeaderManagement对象", description="领导管理表")
public class TLeaderManagement extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 关联用户表的用户ID
     */
    @ApiModelProperty(value = "关联用户表的用户ID")
    private Long userId;

    /**
     * 领导姓名(不能录入同一领导姓名)
     */
    @ApiModelProperty(value = "领导姓名(不能录入同一领导姓名)")
    private String leaderName;

    /**
     * 排序(不能录入同一排序号)
     */
    @ApiModelProperty(value = "排序(不能录入同一排序号)")
    private Integer sort;

    /**
     * 启用日期
     */
    @ApiModelProperty(value = "启用日期")
    private LocalDateTime enableTime;

    /**
     * 停用日期
     */
    @ApiModelProperty(value = "停用日期")
    private LocalDateTime stopTime;

    /**
     * 分组
     */
    @ApiModelProperty(value = "分组")
    private String groupBy;

    /**
     * 状态
     */
//    @ApiModelProperty(value = "状态")
//    private Integer status;


}
