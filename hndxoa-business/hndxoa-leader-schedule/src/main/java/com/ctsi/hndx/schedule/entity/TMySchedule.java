package com.ctsi.hndx.schedule.entity;

import com.ctsi.hndx.common.BaseEntity;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 我的日程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TMySchedule对象", description="我的日程信息表")
public class TMySchedule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 日程内容
     */
    @ApiModelProperty(value = "日程内容")
    private String scheduleContent;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 重要标志
     */
    @ApiModelProperty(value = "重要标志")
    private String importantSigns;


}
