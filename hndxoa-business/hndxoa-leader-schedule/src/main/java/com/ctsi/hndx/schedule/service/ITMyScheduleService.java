package com.ctsi.hndx.schedule.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.schedule.entity.TMySchedule;
import com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 我的日程信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ITMyScheduleService extends SysBaseServiceI<TMySchedule> {


    /**
     * 分页查询
     */
    PageResult<TMyScheduleDTO> queryListPage(TMyScheduleDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TMyScheduleDTO> queryList(TMyScheduleDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TMyScheduleDTO findOne(Long id);

    /**
     * 新增
     */
    TMyScheduleDTO create(TMyScheduleDTO entity);


    /**
     * 更新
     */
    int update(TMyScheduleDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTMyScheduleId
     */
    boolean existByTMyScheduleId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TMyScheduleDTO> dataList);

    /**
     * 查询时间范围内我的日程
     * @param tMyScheduleDTO
     * @return
     */
    List<TMyScheduleDTO> queryTMyScheduleListByTime(TMyScheduleDTO tMyScheduleDTO);


}
