package com.ctsi.hndx.schedule.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.schedule.entity.TLeaderEntrust;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 领导委托信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface TLeaderEntrustMapper extends MybatisBaseMapper<TLeaderEntrust> {

    /**
     * 查询本单位所有领导委托信息
     * @param iPage
     * @param leaderEntrustDTO
     * @return
     */
    IPage<TLeaderEntrustDTO> queryPageLeaderEntrustInUnit(IPage iPage, @Param("dto") TLeaderEntrustDTO leaderEntrustDTO);

}
