package com.ctsi.hndx.schedule.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "领导日程导出", description = "领导日程导出")
public class LeaderScheduleExportDTO {
    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    @ExcelProperty("领导姓名")
    private String leaderName;

    /**
     * 活动内容
     */
    @ApiModelProperty(value = "活动内容")
    @ExcelProperty("活动内容")
    private String activityContent;

    /**
     * 活动地点
     */
    @ApiModelProperty(value = "活动地点")
    @ExcelProperty("活动地点")
    private String activityPlace;

    /**
     * 承办单位
     */
    @ApiModelProperty(value = "承办单位")
    @ExcelProperty("承办单位")
    private String caterCompany;

    /**
     * 开始时间(一次只能录入一天的日程)
     */
    @ApiModelProperty(value = "开始时间(一次只能录入一天的日程)")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间(一次只能录入一天的日程)
     */
    @ApiModelProperty(value = "结束时间(一次只能录入一天的日程)")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @ExcelProperty("联系人名称")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty("联系电话")
    private String mobile;

    /**
     * 其他出席领导
     */
    @ApiModelProperty(value = "其他出席领导")
    @ExcelProperty("其他出席领导")
    private String otherLeaders;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty("备注")
    private String remark;

}
