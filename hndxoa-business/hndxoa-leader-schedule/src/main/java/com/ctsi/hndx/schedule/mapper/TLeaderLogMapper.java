package com.ctsi.hndx.schedule.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndx.schedule.entity.TLeaderLog;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 领导日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface TLeaderLogMapper extends MybatisBaseMapper<TLeaderLog> {

    /**
     * 查询根据领导ID集合查询日志
     * @param tLeaderLogDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<TLeaderLogDTO> getLeaderWeekLog(@Param("dto")TLeaderLogDTO tLeaderLogDTO);

}
