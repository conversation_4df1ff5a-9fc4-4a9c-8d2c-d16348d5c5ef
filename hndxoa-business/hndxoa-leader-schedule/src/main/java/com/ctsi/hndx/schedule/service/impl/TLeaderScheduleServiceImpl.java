package com.ctsi.hndx.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.schedule.entity.TLeaderSchedule;
import com.ctsi.hndx.schedule.entity.dto.LeaderScheduleExportDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.hndx.schedule.mapper.TLeaderScheduleMapper;
import com.ctsi.hndx.schedule.service.ITLeaderEntrustService;
import com.ctsi.hndx.schedule.service.ITLeaderManagementService;
import com.ctsi.hndx.schedule.service.ITLeaderScheduleService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 领导日程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */

@Slf4j
@Service
public class TLeaderScheduleServiceImpl extends SysBaseServiceImpl<TLeaderScheduleMapper, TLeaderSchedule> implements ITLeaderScheduleService {

    @Autowired
    private TLeaderScheduleMapper tLeaderScheduleMapper;

    @Autowired
    private ITLeaderManagementService itLeaderManagementService;

    @Autowired
    private ITLeaderEntrustService itLeaderEntrustService;

    @Autowired
    private ExportToExcelService exportToExcelService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderScheduleDTO> queryListPage(TLeaderScheduleDTO entityDTO, BasePageForm basePageForm) {
        // 判断当前登录用户ID是否存在
        if (Objects.isNull(entityDTO.getUserId())) {
            Long loginId = SecurityUtils.getCurrentUserId();
            entityDTO.setUserId(loginId);
        }

        // 如果登录的人是领导，则查询其所在分组的所有日程信息
        if (itLeaderManagementService.existAllByUserId(entityDTO.getUserId())) {
            return this.querySchedulePageByLeader(entityDTO, basePageForm);
        }

        // 如果登录的是被委托人，则查询其委托领导的日程信息
        else if (itLeaderEntrustService.isConsigneeInAllLeaderEntrustRel(entityDTO.getUserId())) {
            return this.querySchedulePageByLiaison(entityDTO, basePageForm);
        }

        // 如果既不是领导又不是被委托人，则直接返回
        else {
            return new PageResult<>();
        }
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLeaderScheduleDTO> queryList(TLeaderScheduleDTO entityDTO) {
        LambdaQueryWrapper<TLeaderSchedule> queryWrapper = new LambdaQueryWrapper();
            List<TLeaderSchedule> listData = tLeaderScheduleMapper.selectList(queryWrapper);
            List<TLeaderScheduleDTO> TLeaderScheduleDTOList = ListCopyUtil.copy(listData, TLeaderScheduleDTO.class);
        return TLeaderScheduleDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLeaderScheduleDTO findOne(Long id) {
        TLeaderSchedule  tLeaderSchedule =  tLeaderScheduleMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLeaderSchedule,TLeaderScheduleDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLeaderScheduleDTO create(TLeaderScheduleDTO entityDTO) {
       TLeaderSchedule tLeaderSchedule =  BeanConvertUtils.copyProperties(entityDTO,TLeaderSchedule.class);
        save(tLeaderSchedule);
        return  BeanConvertUtils.copyProperties(tLeaderSchedule,TLeaderScheduleDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLeaderScheduleDTO entity) {
        TLeaderSchedule tLeaderSchedule = BeanConvertUtils.copyProperties(entity,TLeaderSchedule.class);
        return tLeaderScheduleMapper.updateById(tLeaderSchedule);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLeaderScheduleMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLeaderScheduleId
     * @return
     */
    @Override
    public boolean existByTLeaderScheduleId(Long TLeaderScheduleId) {
        if (TLeaderScheduleId != null) {
            LambdaQueryWrapper<TLeaderSchedule> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLeaderSchedule::getId, TLeaderScheduleId);
            List<TLeaderSchedule> result = tLeaderScheduleMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLeaderScheduleDTO> dataList) {
        List<TLeaderSchedule> result = ListCopyUtil.copy(dataList, TLeaderSchedule.class);
        return saveBatch(result);
    }


    /**
     * 获取前一天的所有日程
     * @return
     */
    @Override
    public List<TLeaderScheduleDTO> getYesterdaySchedule() {
        return tLeaderScheduleMapper.getYesterdaySchedule();
    }

    /**
     * 获取时间范围内受委托人关联领导的日程信息
     * @param tLeaderScheduleDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryScheduleListByTime(TLeaderScheduleDTO tLeaderScheduleDTO) {
        if (Objects.isNull(tLeaderScheduleDTO.getUserId())) {
            throw new BusinessException("userId不允许为空");
        }

        // 如果登录的人是领导，则查询其所在分组的所有日程信息
        if (itLeaderManagementService.existAllByUserId(tLeaderScheduleDTO.getUserId())) {
            return this.queryScheduleListByLeader(tLeaderScheduleDTO);
        }

        // 如果登录的是被委托人，则查询其委托领导的日程信息
        else if (itLeaderEntrustService.isConsigneeInAllLeaderEntrustRel(tLeaderScheduleDTO.getUserId())) {
            return this.queryScheduleListByLiaison(tLeaderScheduleDTO);
        }

        // 如果既不是领导又不是被委托人，则直接返回
        else {
            return new ArrayList<>();
        }
    }

    /**
     * 查询领导日程(身份为领导)
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderScheduleDTO> querySchedulePageByLeader(TLeaderScheduleDTO entityDTO, BasePageForm basePageForm) {

        int andFlag = 0;
        List<TLeaderManagementDTO> leaderManagementDTOList = itLeaderManagementService.getAllLeadersInGroup(
                entityDTO.getUserId());
        if (CollectionUtils.isEmpty(leaderManagementDTOList)) {
            return new PageResult<>();
        }

        LambdaQueryWrapper<TLeaderSchedule> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotBlank(entityDTO.getLeaderName())) {
            queryWrapper.like(TLeaderSchedule::getLeaderName, entityDTO.getLeaderName());
        }
        if (StringUtils.isNotBlank(entityDTO.getActivityContent())) {
            queryWrapper.like(TLeaderSchedule::getActivityContent, entityDTO.getActivityContent());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(TLeaderSchedule::getStartTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(TLeaderSchedule::getEndTime, entityDTO.getEndTime());
        }

        // 需要按组区分数据
        Map<String, List<TLeaderManagementDTO>> groupLeaderMap = leaderManagementDTOList.stream()
                .collect(Collectors.groupingBy(TLeaderManagementDTO::getGroupBy));

        // 按组处理数据
        for (Map.Entry<String, List<TLeaderManagementDTO>> map : groupLeaderMap.entrySet()) {
            // 获取该领导在该组的信息
            TLeaderManagementDTO tLeaderManagementDTO = map.getValue().stream().filter(i ->
                    entityDTO.getUserId().equals(i.getUserId())).collect(Collectors.toList()).get(0);

            // 判断该领导在该组是否激活
            if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(tLeaderManagementDTO.getEnableTime())) {
                continue;
            }
            List<Long> leaderIdList = map.getValue().stream().map(i -> i.getId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaderIdList)) {
                continue;
            }

            if (andFlag == 0) {
                // 判断该领导是否被禁用
                if (nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                    queryWrapper.and(wq-> wq.in(TLeaderSchedule::getLeaderId, leaderIdList));
                } else {
                    queryWrapper.and(wq-> wq.in(TLeaderSchedule::getLeaderId, leaderIdList)
                            .le(TLeaderSchedule::getCreateTime, tLeaderManagementDTO.getStopTime()));
                }
            } else {
                // 判断该领导是否被禁用
                if (nowTime.isBefore(tLeaderManagementDTO.getStopTime())) {
                    queryWrapper.or().and(wq-> wq.in(TLeaderSchedule::getLeaderId, leaderIdList));
                } else {
                    queryWrapper.or().and(wq-> wq.in(TLeaderSchedule::getLeaderId, leaderIdList)
                            .le(TLeaderSchedule::getCreateTime, tLeaderManagementDTO.getStopTime()));
                }
            }

            andFlag = 1;
        }
        // 按开始时间倒序
        queryWrapper.orderByDesc(TLeaderSchedule::getStartTime);
        IPage<TLeaderSchedule> pageData = tLeaderScheduleMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage<TLeaderScheduleDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLeaderScheduleDTO.class));
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 查询领导日程(身份为联络人)
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderScheduleDTO> querySchedulePageByLiaison(TLeaderScheduleDTO entityDTO, BasePageForm basePageForm) {

        List<TLeaderManagementDTO> leaderAndConsigneeWeekScheduleList = itLeaderManagementService.getAllLeadersByConsigneeId(
                entityDTO.getUserId());

        List<Long> leaderIdList = leaderAndConsigneeWeekScheduleList.stream()
                .map(x -> x.getId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderIdList)) {
            return new PageResult<>();
        }

        //设置条件
        LambdaQueryWrapper<TLeaderSchedule> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TLeaderSchedule::getLeaderId, leaderIdList);
        if (StringUtils.isNotBlank(entityDTO.getLeaderName())) {
            queryWrapper.like(TLeaderSchedule::getLeaderName, entityDTO.getLeaderName());
        }
        if (StringUtils.isNotBlank(entityDTO.getActivityContent())) {
            queryWrapper.like(TLeaderSchedule::getActivityContent, entityDTO.getActivityContent());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(TLeaderSchedule::getStartTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(TLeaderSchedule::getStartTime, entityDTO.getEndTime());
        }
        // 按开始时间倒序
        queryWrapper.orderByDesc(TLeaderSchedule::getStartTime);
        IPage<TLeaderSchedule> pageData = tLeaderScheduleMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //返回
        IPage<TLeaderScheduleDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLeaderScheduleDTO.class));

        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 查询领导日程(身份为领导)
     * @param tLeaderScheduleDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryScheduleListByLeader(TLeaderScheduleDTO tLeaderScheduleDTO) {
        List<TLeaderManagementDTO> leaderAndConsigneeWeekScheduleList = new ArrayList<>();

        List<TLeaderManagementDTO> leaderManagementDTOList = itLeaderManagementService.getAllLeadersInGroup(
                tLeaderScheduleDTO.getUserId());

        // 需要按组区分数据
        Map<String, List<TLeaderManagementDTO>> groupLeaderMap = leaderManagementDTOList.stream()
                .collect(Collectors.groupingBy(TLeaderManagementDTO::getGroupBy));

        // 按组处理数据
        for (Map.Entry<String, List<TLeaderManagementDTO>> map : groupLeaderMap.entrySet()) {
            // 获取该领导在该组的信息
            TLeaderManagementDTO tLeaderManagementDTO = map.getValue().stream().filter(i ->
                    tLeaderScheduleDTO.getUserId().equals(i.getUserId())).collect(Collectors.toList()).get(0);

            // 判断该领导在该组是否激活
            if (Objects.isNull(tLeaderManagementDTO.getEnableTime()) || Objects.isNull(tLeaderManagementDTO.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(tLeaderManagementDTO.getEnableTime())) {
                continue;
            }

            for (TLeaderManagementDTO leader : map.getValue()) {
                // 查询该领导在时间范围内的所有日程
                TLeaderScheduleDTO dto = new TLeaderScheduleDTO();
                dto.setLeaderId(leader.getId());
                // 如果用户被禁用，需要添加禁用时间查询条件
                if (nowTime.isAfter(tLeaderManagementDTO.getStopTime())) {
                    dto.setLeaderStopTime(tLeaderManagementDTO.getStopTime());
                }
                dto.setStartTime(tLeaderScheduleDTO.getStartTime());
                dto.setEndTime(tLeaderScheduleDTO.getEndTime());
                List<TLeaderScheduleDTO> leaderWeekScheduleList = tLeaderScheduleMapper.getLeaderWeekSchedule(dto);
                leader.setTLeaderScheduleDTOList(leaderWeekScheduleList);
                leaderAndConsigneeWeekScheduleList.add(leader);
            }
        }
        return leaderAndConsigneeWeekScheduleList;
    }


    /**
     * 查询领导日程(身份为联络人)
     * @param tLeaderScheduleDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryScheduleListByLiaison(TLeaderScheduleDTO tLeaderScheduleDTO) {

        List<TLeaderManagementDTO> leaderAndConsigneeWeekScheduleList = itLeaderManagementService.getAllLeadersByConsigneeId(
                tLeaderScheduleDTO.getUserId());
        for (TLeaderManagementDTO leader : leaderAndConsigneeWeekScheduleList) {
            // 判断该领导是否激活
            if (Objects.isNull(leader.getEnableTime()) || Objects.isNull(leader.getStopTime())) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(leader.getEnableTime())) {
                continue;
            }

            // 查询该组领导组的时间范围内的所有日程
            TLeaderScheduleDTO dto = new TLeaderScheduleDTO();
            dto.setLeaderId(leader.getId());
            if (nowTime.isAfter(leader.getStopTime())) {
                dto.setLeaderStopTime(leader.getStopTime());
            }
            dto.setStartTime(tLeaderScheduleDTO.getStartTime());
            dto.setEndTime(tLeaderScheduleDTO.getEndTime());
            List<TLeaderScheduleDTO> leaderWeekScheduleList = tLeaderScheduleMapper.getLeaderWeekSchedule(dto);
            leader.setTLeaderScheduleDTOList(leaderWeekScheduleList);
        }
        return leaderAndConsigneeWeekScheduleList;
    }

    /**
     * 领导日程导出
     *
     * @param response
     * @return
     */
    @Override
    public Boolean exportLeaderSchedule(HttpServletResponse response) {
        LambdaQueryWrapper<TLeaderSchedule> queryWrapper = new LambdaQueryWrapper();
        List<TLeaderSchedule> listData = tLeaderScheduleMapper.selectListNoAdd(queryWrapper);
        List<LeaderScheduleExportDTO> TLeaderScheduleDTOList = ListCopyUtil.copy(listData, LeaderScheduleExportDTO.class);

        Boolean bool = null;
        try {
            bool = exportToExcelService.exportToExcel(TLeaderScheduleDTOList, LeaderScheduleExportDTO.class, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bool;
    }
}
