package com.ctsi.hndx.schedule.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 领导委托信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLeaderEntrust对象", description="领导委托信息表")
public class TLeaderEntrust extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被委托人ID
     */
    @ApiModelProperty(value = "被委托人ID")
    private Long entrustedId;

    /**
     * 被委托人名称
     */
    @ApiModelProperty(value = "被委托人名称")
    private String entrustedName;

    /**
     * 领导ID
     */
    @ApiModelProperty(value = "领导ID(领导表主键)")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;


}
