package com.ctsi.hndx.schedule.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.hndx.schedule.service.ITLeaderScheduleService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLeaderSchedule")
@Api(value = "领导日程表", tags = "领导日程表接口")
public class TLeaderScheduleController extends BaseController {

    private static final String ENTITY_NAME = "tLeaderSchedule";

    @Autowired
    private ITLeaderScheduleService tLeaderScheduleService;



    /**
     *  新增领导日程表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导日程表批量数据")
    public ResultVO createBatch(@RequestBody List<TLeaderScheduleDTO> tLeaderScheduleList) {
       Boolean  result = tLeaderScheduleService.insertBatch(tLeaderScheduleList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导日程表数据")
    public ResultVO<TLeaderScheduleDTO> create(@RequestBody TLeaderScheduleDTO tLeaderScheduleDTO)  {
        TLeaderScheduleDTO result = tLeaderScheduleService.create(tLeaderScheduleDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新领导日程表数据")
    public ResultVO update(@RequestBody TLeaderScheduleDTO tLeaderScheduleDTO) {
	    Assert.notNull(tLeaderScheduleDTO.getId(), "general.IdNotNull");
        int count = tLeaderScheduleService.update(tLeaderScheduleDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除领导日程表数据")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLeaderScheduleService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TLeaderScheduleDTO tLeaderScheduleDTO = tLeaderScheduleService.findOne(id);
        return ResultVO.success(tLeaderScheduleDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLeaderSchedulePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TLeaderScheduleDTO>> queryTLeaderSchedulePage(TLeaderScheduleDTO tLeaderScheduleDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLeaderScheduleService.queryListPage(tLeaderScheduleDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLeaderSchedule")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TLeaderScheduleDTO>> queryTLeaderSchedule(TLeaderScheduleDTO tLeaderScheduleDTO) {
       List<TLeaderScheduleDTO> list = tLeaderScheduleService.queryList(tLeaderScheduleDTO);
       return ResultVO.success(list);
   }

    /**
     * 获取时间范围内受委托人关联领导的日程信息
     */
    @PostMapping("/queryScheduleListByTime")
    @ApiOperation(value = "获取时间范围内受委托人关联领导的日程信息", notes = "传入参数")
    public ResultVO<List<TLeaderManagementDTO>> queryScheduleListByTime(@RequestBody TLeaderScheduleDTO tLeaderScheduleDTO) {
        // 判断当前登录用户ID是否存在
        if (Objects.isNull(tLeaderScheduleDTO.getUserId())) {
            Long loginId = SecurityUtils.getCurrentUserId();
            tLeaderScheduleDTO.setUserId(loginId);
        }
        List<TLeaderManagementDTO> list = tLeaderScheduleService.queryScheduleListByTime(tLeaderScheduleDTO);
        return ResultVO.success(list);
    }

    /**
     * 领导日程导出
     */
    @GetMapping("/exportLeaderSchedule")
    @ApiOperation(value = "领导日程导出", notes = "传入参数")
    public ResultVO exportLeaderSchedule(HttpServletResponse response) {
        Boolean bool = tLeaderScheduleService.exportLeaderSchedule(response);
        return ResultVO.success(bool);
    }
}
