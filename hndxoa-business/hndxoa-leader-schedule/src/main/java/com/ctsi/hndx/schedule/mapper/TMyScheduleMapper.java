package com.ctsi.hndx.schedule.mapper;

import com.ctsi.hndx.schedule.entity.TMySchedule;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 我的日程信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface TMyScheduleMapper extends MybatisBaseMapper<TMySchedule> {

    /**
     * 查询时间范围内我的日日程
     * @param tMyScheduleDTO
     * @return
     */
    List<TMyScheduleDTO> queryMyScheduleList(@Param("dto")TMyScheduleDTO tMyScheduleDTO);
}
