package com.ctsi.hndx.schedule.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.schedule.entity.dto.TMyScheduleDTO;
import com.ctsi.hndx.schedule.service.ITMyScheduleService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tMySchedule")
@Api(value = "我的日程信息表", tags = "我的日程信息表接口")
public class TMyScheduleController extends BaseController {

    private static final String ENTITY_NAME = "tMySchedule";

    @Autowired
    private ITMyScheduleService tMyScheduleService;



    /**
     *  新增我的日程信息表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增我的日程信息表批量数据")

    public ResultVO createBatch(@RequestBody List<TMyScheduleDTO> tMyScheduleList) {
       Boolean  result = tMyScheduleService.insertBatch(tMyScheduleList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增我的日程信息表数据")
    public ResultVO<TMyScheduleDTO> create(@RequestBody TMyScheduleDTO tMyScheduleDTO)  {
        TMyScheduleDTO result = tMyScheduleService.create(tMyScheduleDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新我的日程信息表数据")
    public ResultVO update(@RequestBody TMyScheduleDTO tMyScheduleDTO) {
	    Assert.notNull(tMyScheduleDTO.getId(), "general.IdNotNull");
        int count = tMyScheduleService.update(tMyScheduleDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除我的日程信息表数据")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tMyScheduleService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TMyScheduleDTO tMyScheduleDTO = tMyScheduleService.findOne(id);
        return ResultVO.success(tMyScheduleDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTMySchedulePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TMyScheduleDTO>> queryTMySchedulePage(TMyScheduleDTO tMyScheduleDTO, BasePageForm basePageForm) {
        return ResultVO.success(tMyScheduleService.queryListPage(tMyScheduleDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTMySchedule")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TMyScheduleDTO>> queryTMySchedule(TMyScheduleDTO tMyScheduleDTO) {
       List<TMyScheduleDTO> list = tMyScheduleService.queryList(tMyScheduleDTO);
       return ResultVO.success(list);
   }

    @PostMapping("/queryTMyScheduleListByTime")
    @ApiOperation(value = "查询时间范围内我的日程", notes = "传入参数")
    public ResultVO<List<TMyScheduleDTO>> queryTMyScheduleListByTime(@RequestBody TMyScheduleDTO tMyScheduleDTO) {
        List<TMyScheduleDTO> list = tMyScheduleService.queryTMyScheduleListByTime(tMyScheduleDTO);
        return ResultVO.success(list);
    }

}
