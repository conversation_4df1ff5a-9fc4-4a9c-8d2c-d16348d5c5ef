package com.ctsi.hndx.schedule.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 我的日程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TMyScheduleDTO对象", description = "我的日程信息表")
public class TMyScheduleDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 日程内容
     */
    @ApiModelProperty(value = "日程内容")
    private String scheduleContent;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 重要标志
     */
    @ApiModelProperty(value = "重要标志")
    private String importantSigns;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "单位ID")
    private Long companyId;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;


    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String databaseType;
}
