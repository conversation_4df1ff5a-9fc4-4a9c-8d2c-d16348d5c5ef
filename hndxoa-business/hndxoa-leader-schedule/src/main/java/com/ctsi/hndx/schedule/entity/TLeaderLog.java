package com.ctsi.hndx.schedule.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 领导日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLeaderLog对象", description="领导日志表")
public class TLeaderLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 领导ID
     */
    @ApiModelProperty(value = "领导ID(领导表主键ID)")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    /**
     * 日志内容
     */
    @ApiModelProperty(value = "日志内容")
    private String logContent;

    /**
     * 补录时间(不能大于当前日期)
     */
    @ApiModelProperty(value = "补录时间(不能大于当前日期)")
    private LocalDateTime makeupTime;

    /**
     * 时间段(上午、下午、全天)
     */
    @ApiModelProperty(value = "时间段(上午、下午、全天)")
    private Integer timePeriod;

    /**
     * 录入方式
     */
    @ApiModelProperty(value = "录入方式(1:补录 2:自动生成)")
    private Integer recordType;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;


}
