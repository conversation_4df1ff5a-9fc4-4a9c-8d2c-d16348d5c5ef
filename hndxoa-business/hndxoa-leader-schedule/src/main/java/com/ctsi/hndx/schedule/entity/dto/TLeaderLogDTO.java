package com.ctsi.hndx.schedule.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 领导日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLeaderLogDTO对象", description="领导日志表")
public class TLeaderLogDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 领导ID
     */
    @ApiModelProperty(value = "领导ID(领导表主键ID)")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    /**
     * 日志内容
     */
    @ApiModelProperty(value = "日志内容")
    private String logContent;

    /**
     * 补录时间(不能大于当前日期)
     */
    @ApiModelProperty(value = "补录时间(不能大于当前日期)")
    private LocalDateTime makeupTime;

    /**
     * 时间段(上午、下午、全天)
     */
    @ApiModelProperty(value = "时间段(上午、下午、全天)")
    private Integer timePeriod;

    /**
     * 录入方式
     */
    @ApiModelProperty(value = "录入方式(1:补录 2:自动生成)")
    private Integer recordType;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "关联用户表ID")
    private Long userId;

    @ApiModelProperty(value = "领导ID集合")
    private List<Long> leaderIdList;

    @ApiModelProperty(value = "部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "单位ID")
    private Long companyId;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "领导停用时间")
    private LocalDateTime leaderStopTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

}
