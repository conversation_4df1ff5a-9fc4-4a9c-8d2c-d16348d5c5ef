package com.ctsi.hndx.schedule.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderLogDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.hndx.schedule.service.ITLeaderLogService;
import com.ctsi.hndx.schedule.service.ITLeaderScheduleService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLeaderLog")
@Api(value = "领导日志表", tags = "领导日志表接口")
public class TLeaderLogController extends BaseController {

    private static final String ENTITY_NAME = "tLeaderLog";

    @Autowired
    private ITLeaderLogService tLeaderLogService;

    @Autowired
    private ITLeaderScheduleService leaderScheduleService;

    /**
     *  新增领导日志表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导日志表批量数据")

    public ResultVO createBatch(@RequestBody List<TLeaderLogDTO> tLeaderLogList) {
       Boolean  result = tLeaderLogService.insertBatch(tLeaderLogList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导日志表数据")
    public ResultVO<TLeaderLogDTO> create(@RequestBody TLeaderLogDTO tLeaderLogDTO)  {
        TLeaderLogDTO result = tLeaderLogService.create(tLeaderLogDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新领导日志表数据")
    public ResultVO update(@RequestBody TLeaderLogDTO tLeaderLogDTO) {
	    Assert.notNull(tLeaderLogDTO.getId(), "general.IdNotNull");
        int count = tLeaderLogService.update(tLeaderLogDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除领导日志表数据")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLeaderLogService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TLeaderLogDTO tLeaderLogDTO = tLeaderLogService.findOne(id);
        return ResultVO.success(tLeaderLogDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLeaderLogPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TLeaderLogDTO>> queryTLeaderLogPage(TLeaderLogDTO tLeaderLogDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLeaderLogService.queryListPage(tLeaderLogDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLeaderLog")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TLeaderLogDTO>> queryTLeaderLog(TLeaderLogDTO tLeaderLogDTO) {
       List<TLeaderLogDTO> list = tLeaderLogService.queryList(tLeaderLogDTO);
       return ResultVO.success(list);
   }

    /**
     * 获取时间范围内受委托人关联领导的日志信息
     */
    @PostMapping("/queryLogListByTime")
    @ApiOperation(value = "获取时间范围内受委托人关联领导的日志信息", notes = "传入参数")
    public ResultVO<List<TLeaderManagementDTO>> queryLogListByTime(@RequestBody TLeaderLogDTO tLeaderLogDTO) {
        // 判断当前登录用户ID是否存在
        if (Objects.isNull(tLeaderLogDTO.getUserId())) {
            Long loginId = SecurityUtils.getCurrentUserId();
            tLeaderLogDTO.setUserId(loginId);
        }
        List<TLeaderManagementDTO> list = tLeaderLogService.queryLogListByTime(tLeaderLogDTO);
        return ResultVO.success(list);
    }

    /**
     * 前一天的日程转日志接口
     */
    @PostMapping("/scheduleToLog")
    @ApiOperation(value = "前一天的日程转日志接口", notes = "传入参数")
    public ResultVO scheduleToLog() {
        // 获取昨天所有日程记录
        List<TLeaderScheduleDTO> yesterdayScheduleList = leaderScheduleService.getYesterdaySchedule();

        // 日程记录转换为日志记录
        tLeaderLogService.scheduleToLog(yesterdayScheduleList);

        return ResultVO.success();
    }

}
