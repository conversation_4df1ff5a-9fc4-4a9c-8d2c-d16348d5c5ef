/*
package com.ctsi.hndx.schedule.job;

import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import com.ctsi.hndx.schedule.service.ITLeaderLogService;
import com.ctsi.hndx.schedule.service.ITLeaderScheduleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

*/
/**
 * <AUTHOR>
 * @Classname ScheduleToLogJob
 * @Description
 * @Date 2021/12/16/0016 9:56
 *//*

@Component
public class ScheduleJob {

    private static Logger logger = LoggerFactory.getLogger(ScheduleJob.class);

    @Autowired
    private ITLeaderScheduleService leaderScheduleService;

    @Autowired
    private ITLeaderLogService itLeaderLogService;

    @XxlJob("scheduleToLogJobHandler")
    public void scheduleToLogJobHandler() throws Exception {
        logger.info("定时任务：日程转换日志开始");
        XxlJobHelper.log("定时任务：日程转换日志开始");

        // 获取昨天所有日程记录
        List<TLeaderScheduleDTO> yesterdayScheduleList = leaderScheduleService.getYesterdaySchedule();

        // 日程记录转换为日志记录
        itLeaderLogService.scheduleToLog(yesterdayScheduleList);

    }
}
*/
