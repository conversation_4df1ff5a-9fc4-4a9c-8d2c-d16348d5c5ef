package com.ctsi.hndx.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.schedule.entity.TLeaderManagement;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.hndx.schedule.mapper.TLeaderManagementMapper;
import com.ctsi.hndx.schedule.service.ITLeaderEntrustService;
import com.ctsi.hndx.schedule.service.ITLeaderManagementService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 领导管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */

@Slf4j
@Service
public class TLeaderManagementServiceImpl extends SysBaseServiceImpl<TLeaderManagementMapper, TLeaderManagement> implements ITLeaderManagementService {

    @Autowired
    private TLeaderManagementMapper tLeaderManagementMapper;

    @Autowired
    private ITLeaderEntrustService itLeaderEntrustService;

    @Autowired
    private ITSysDictRecordService itSysDictRecordService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLeaderManagementDTO> queryListPage(TLeaderManagementDTO entityDTO, BasePageForm basePageForm) {

        // 查询数据字典记录表的主键
        List<Long> dictRecordIdList = itSysDictRecordService.getDictRecordListByDictCode("leaderGroupBy", null)
                .stream().map(i -> i.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dictRecordIdList)) {
            return new PageResult<>();
        }
        entityDTO.setDictRecordIdList(dictRecordIdList);

        IPage<TLeaderManagement> pageData = tLeaderManagementMapper.queryPageLeaderInUnit(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        //返回
        IPage<TLeaderManagementDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TLeaderManagementDTO.class));

        // 新增判断领导状态
        List<TLeaderManagementDTO> collect = data.getRecords().stream().map(i -> {
            if (Objects.isNull(i.getEnableTime()) || Objects.isNull(i.getStopTime())) {
                i.setStatus(SysConstant.LEADER_STOP_STATUS);
                return i;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isAfter(i.getEnableTime()) && nowTime.isBefore(i.getStopTime())) {
                i.setStatus(SysConstant.LEADER_ENABLE_STATUS);
                return i;
            }
            i.setStatus(SysConstant.LEADER_STOP_STATUS);
            return i;
        }).collect(Collectors.toList());
        return new PageResult<>(collect, data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> queryList(TLeaderManagementDTO entityDTO) {
        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(entityDTO.getUserId())) {
            queryWrapper.eq(TLeaderManagement::getUserId, entityDTO.getUserId());
        }
        List<TLeaderManagement> listData = tLeaderManagementMapper.selectList(queryWrapper);
        List<TLeaderManagementDTO> TLeaderManagementDTOList = ListCopyUtil.copy(listData, TLeaderManagementDTO.class);
        return TLeaderManagementDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLeaderManagementDTO findOne(Long id) {
        TLeaderManagement  tLeaderManagement =  tLeaderManagementMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLeaderManagement,TLeaderManagementDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLeaderManagementDTO create(TLeaderManagementDTO entityDTO) {
        log.info("TLeaderManagementServiceImpl.create entityDTO = {}", entityDTO);
        if (Objects.isNull(entityDTO.getUserId()) || StringUtils.isBlank(entityDTO.getGroupBy())) {
            throw new BusinessException("用户ID和分组不能为空");
        }
        // 在同一组内不允许出现同个领导
        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderManagement::getUserId, entityDTO.getUserId());
        queryWrapper.eq(TLeaderManagement::getGroupBy, entityDTO.getGroupBy());
        Integer count = tLeaderManagementMapper.selectCountOnlyAddTenantId(queryWrapper);
        if (count > 0) {
            throw new BusinessException("该领导在该分组已存在");
        }

        // 如果启用日期不为空，则需要添加默认停用日期为 2099-12-31 00:00:00
        if (Objects.nonNull(entityDTO.getEnableTime())) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime ldt = LocalDateTime.parse("2099-12-31 00:00:00", df);
            entityDTO.setStopTime(ldt);
        }
        this.updateSort(entityDTO);
        TLeaderManagement tLeaderManagement = BeanConvertUtils.copyProperties(entityDTO,TLeaderManagement.class);
        save(tLeaderManagement);
        return  BeanConvertUtils.copyProperties(tLeaderManagement,TLeaderManagementDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLeaderManagementDTO entity) {
        TLeaderManagement tLeaderManagement = BeanConvertUtils.copyProperties(entity,TLeaderManagement.class);
        this.updateSort(entity);
        return tLeaderManagementMapper.updateById(tLeaderManagement);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 删除领导委托关系
        itLeaderEntrustService.deleteEntrustByLeaderId(id);

        return tLeaderManagementMapper.deleteById(id);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> idList) {
        for (Long id : idList) {
            // 删除领导委托关系
            itLeaderEntrustService.deleteEntrustByLeaderId(id);
        }
        tLeaderManagementMapper.deleteBatchIds(idList);
        return true;
    }

    /**
     * 验证是否存在
     *
     * @param TLeaderManagementId
     * @return
     */
    @Override
    public boolean existByTLeaderManagementId(Long TLeaderManagementId) {
        if (TLeaderManagementId != null) {
            LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLeaderManagement::getId, TLeaderManagementId);
            List<TLeaderManagement> result = tLeaderManagementMapper.selectListOnlyAddTenantId(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLeaderManagementDTO> dataList) {
        List<TLeaderManagement> result = ListCopyUtil.copy(dataList, TLeaderManagement.class);
        return saveBatch(result);
    }

    /**
     * 根据userID判断是否是领导(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public Boolean existByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("查询领导管理表的userId不允许为空");
        }
        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderManagement::getUserId, userId);
        List<TLeaderManagement> tLeaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(queryWrapper);
        if (CollectionUtils.isEmpty(tLeaderManagements)) {
            return false;
        }

        // 需要判断该条记录的领导是否在启用日志和禁用日期之间
        List<TLeaderManagement> leaderManagements = tLeaderManagements.stream().filter(i -> {
            if (Objects.isNull(i.getEnableTime()) || Objects.isNull(i.getStopTime())) {
                return false;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isAfter(i.getEnableTime()) && nowTime.isBefore(i.getStopTime())) {
                return true;
            }
            return false;
        }).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(leaderManagements)) {
            return false;
        }
        return true;
    }


    /**
     * 根据userID判断是否是领导(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public Boolean existAllByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("查询领导管理表的userId不允许为空");
        }
        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderManagement::getUserId, userId);
        Integer count = tLeaderManagementMapper.selectCountOnlyAddTenantId(queryWrapper);
        return count > 0;
    }

    /**
     * 查询其委托的领导信息
     * @param leaderManagementDTO
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> getLeadersByConsigneeId(TLeaderManagementDTO leaderManagementDTO) {
        if (Objects.isNull(leaderManagementDTO.getLoginId())) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        Long loginId = leaderManagementDTO.getLoginId();

        // 新增如果该用户为领导时，需要返回自己所在领导表的信息
        if (this.existByUserId(loginId)) {
            LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLeaderManagement::getUserId, loginId);
            if (StringUtils.isNotBlank(leaderManagementDTO.getLeaderName())) {
                queryWrapper.like(TLeaderManagement::getLeaderName, leaderManagementDTO.getLeaderName());
            }
            List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(queryWrapper).stream().filter(i -> {
                // 需要排除被禁用的领导
                if (Objects.isNull(i.getEnableTime()) || Objects.isNull(i.getStopTime())) {
                    return false;
                }
                LocalDateTime nowTime = LocalDateTime.now();
                if (nowTime.isAfter(i.getEnableTime()) && nowTime.isBefore(i.getStopTime())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
        }

        // 获取领导委托信息
        List<TLeaderEntrustDTO> tLeaderEntrustDTOList = itLeaderEntrustService.getLeaderEntrustByEntrustId(loginId);
        if (CollectionUtils.isEmpty(tLeaderEntrustDTOList)) {
            return new ArrayList<>();
        }

        // 获取领导信息
        List<Long> leaderIdList = tLeaderEntrustDTOList.stream()
                .map(x -> x.getLeaderId()).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(TLeaderManagement::getId, leaderIdList);
        if (StringUtils.isNotBlank(leaderManagementDTO.getLeaderName())) {
            lambdaQueryWrapper.like(TLeaderManagement::getLeaderName, leaderManagementDTO.getLeaderName());
        }
        List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(lambdaQueryWrapper);
        return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
    }

    /**
     * 获取该领导所在组的所有领导(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> getLeadersInGroup(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("查询领导管理表的userId不允许为空");
        }
        // 根据userId查询领导管理表信息
        List<TLeaderManagementDTO> leaderManagementDTOList = new ArrayList<>();

        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderManagement::getUserId, userId);
        List<TLeaderManagement> tLeaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(queryWrapper);

        // 如果没有查到该领导，则返回为空
        if (CollectionUtils.isEmpty(tLeaderManagements)) {
            return leaderManagementDTOList;
        }

        // 需要过滤该领导被停用的分组
        List<String> groupList = tLeaderManagements.stream().filter(i -> {
            if (Objects.isNull(i.getEnableTime()) || Objects.isNull(i.getStopTime())) {
                return false;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isAfter(i.getEnableTime()) && nowTime.isBefore(i.getStopTime())) {
                return true;
            }
            return false;
        }).map(x -> x.getGroupBy()).distinct().collect(Collectors.toList());
        // 如果该领导没有分组号，则返回为空
        if (CollectionUtils.isEmpty(groupList)) {
            return new ArrayList<>();
        }

        // 查询该领导所在组的所有领导信息
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(TLeaderManagement::getGroupBy, groupList);
        // 排除组内被禁用的领导
        LocalDateTime localDateTime = LocalDateTime.now();
        lambdaQueryWrapper.le(TLeaderManagement::getEnableTime, localDateTime);
        lambdaQueryWrapper.ge(TLeaderManagement::getStopTime, localDateTime);
        List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(lambdaQueryWrapper);
        return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
    }


    /**
     * 获取该领导所在组的所有领导(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> getAllLeadersInGroup(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("查询领导管理表的userId不允许为空");
        }
        // 根据userId查询领导管理表信息
        LambdaQueryWrapper<TLeaderManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLeaderManagement::getUserId, userId);
        List<TLeaderManagement> tLeaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(queryWrapper);

        // 如果没有查到该领导，则返回为空
        if (CollectionUtils.isEmpty(tLeaderManagements)) {
            return new ArrayList<>();
        }

        // 获取分组号
        List<String> groupList = tLeaderManagements.stream().map(x -> x.getGroupBy()).distinct().collect(Collectors.toList());
        // 如果该领导没有分组号，则返回为空
        if (CollectionUtils.isEmpty(groupList)) {
            return new ArrayList<>();
        }

        // 查询该领导所在组的所有领导信息
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(TLeaderManagement::getGroupBy, groupList);
        List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(lambdaQueryWrapper);
        return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
    }

    /**
     * 根据用户表ID查询其委托的领导信息(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> getLeadersByConsigneeId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        // 获取领导委托信息
        List<TLeaderEntrustDTO> tLeaderEntrustDTOList = itLeaderEntrustService.getLeaderEntrustByEntrustId(userId);

        if (CollectionUtils.isEmpty(tLeaderEntrustDTOList)) {
            return new ArrayList<>();
        }

        // 获取领导信息
        List<Long> leaderIdList = tLeaderEntrustDTOList.stream()
                .map(x -> x.getLeaderId()).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(TLeaderManagement::getId, leaderIdList);
        List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(lambdaQueryWrapper);
        return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
    }


    /**
     * 根据用户表ID查询其委托的领导信息(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    @Override
    public List<TLeaderManagementDTO> getAllLeadersByConsigneeId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("被委托人的entrustedId不允许为空");
        }
        // 获取领导委托信息
        List<TLeaderEntrustDTO> tLeaderEntrustDTOList = itLeaderEntrustService.getAllLeaderEntrustByEntrustId(userId);
        if (CollectionUtils.isEmpty(tLeaderEntrustDTOList)) {
            return new ArrayList<>();
        }
        List<Long> leaderIdList = tLeaderEntrustDTOList.stream()
                .map(x -> x.getLeaderId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderIdList)) {
            return new ArrayList<>();
        }

        // 获取领导信息
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(TLeaderManagement::getId, leaderIdList);
        List<TLeaderManagement> leaderManagements = tLeaderManagementMapper.selectListOnlyAddTenantId(lambdaQueryWrapper);
        return ListCopyUtil.copy(leaderManagements, TLeaderManagementDTO.class);
    }


    /**
     * 根据领导ID启用停用该领导
     * @param dto
     * @return
     */
    @Override
    public Boolean setLeaderStatus(TLeaderManagementDTO dto) {
        if (Objects.isNull(dto.getStatus())) {
            log.info("领导状态不允许为空");
            throw new BusinessException("领导状态不允许为空");
        }

        // 查询领导
        TLeaderManagementDTO leaderDTO = this.findOne(dto.getId());
        if (Objects.isNull(leaderDTO)) {
            log.info("未查询到该领导：leaderId = {}", dto.getId());
            throw new BusinessException("未查询到该领导");
        }

        // 启用领导
        if (SysConstant.LEADER_ENABLE_STATUS.equals(dto.getStatus())) {
            leaderDTO.setEnableTime(LocalDateTime.now());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime ldt = LocalDateTime.parse("2099-12-31 00:00:00", df);
            leaderDTO.setStopTime(ldt);
        }

        // 停用领导
        if (SysConstant.LEADER_STOP_STATUS.equals(dto.getStatus())) {
//            leaderDTO.setStatus(dto.getStatus());
            leaderDTO.setStopTime(LocalDateTime.now());
        }

        this.update(leaderDTO);
        return true;
    }

    /**
     * 修改领导管理表排序
     * @param entity
     * @return
     */
    @Override
    public Boolean updateSort(TLeaderManagementDTO entity){
        // 先查询当前公司下该排序号是否存在
        Boolean result = this.getLeaderSort(entity.getSort(), entity.getGroupBy());
        //如果存在，则把该公司下的大于等于该排序的所有排序号 + 1
        if (!result){
            tLeaderManagementMapper.updataSort(SortEnum.builder()
                    .sort(entity.getSort())
                    .companyId(SecurityUtils.getCurrentCompanyId())
                    .id(entity.getId())
                    .tableName("t_leader_management")
                    .sortName("sort")
                    .additionOrsubtraction("+")
                    .build());
        }
        return true;
    }

    /**
     * 获取排序号并判断是否重复
     * @param sort
     * @return
     */
    @Override
    public Boolean getLeaderSort(Integer sort, String groupBy){
        Long companyId = SecurityUtils.getCurrentCompanyId();
        LambdaQueryWrapper<TLeaderManagement> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TLeaderManagement::getCompanyId,companyId);
        lambdaQueryWrapper.eq(TLeaderManagement::getSort,sort);
        // fix：公文包领导管理，新建不同分组的领导，排序号应允许相同，而不是更新原有相同编号
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(groupBy),TLeaderManagement::getGroupBy, groupBy);
        int count = tLeaderManagementMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            return false;
        }else {
            return true;
        }
    }
}
