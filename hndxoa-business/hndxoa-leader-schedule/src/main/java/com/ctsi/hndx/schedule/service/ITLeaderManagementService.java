package com.ctsi.hndx.schedule.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.schedule.entity.TLeaderManagement;
import com.ctsi.hndx.schedule.entity.dto.TLeaderManagementDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 领导管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ITLeaderManagementService extends SysBaseServiceI<TLeaderManagement> {


    /**
     * 分页查询
     */
    PageResult<TLeaderManagementDTO> queryListPage(TLeaderManagementDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TLeaderManagementDTO> queryList(TLeaderManagementDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TLeaderManagementDTO findOne(Long id);

    /**
     * 新增
     */
    TLeaderManagementDTO create(TLeaderManagementDTO entity);


    /**
     * 更新
     */
    int update(TLeaderManagementDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 批量删除
     * @param idList
     * @return
     */
    boolean batchDelete(List<Long> idList);

     /**
     * 是否存在
     * existByTLeaderManagementId
     */
    boolean existByTLeaderManagementId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TLeaderManagementDTO> dataList);

    /**
     * 根据userID判断是否是领导(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    Boolean existByUserId(Long userId);

    /**
     * 根据userID判断是否是领导(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    Boolean existAllByUserId(Long userId);

    /**
     * 获取该领导所在组的所有领导(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderManagementDTO> getLeadersInGroup(Long userId);

    /**
     * 获取该领导所在组的所有领导(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderManagementDTO> getAllLeadersInGroup(Long userId);

    /**
     * 查询其委托的领导信息(排除被禁用的领导账号)
     * @param leaderManagementDTO
     * @return
     */
    List<TLeaderManagementDTO> getLeadersByConsigneeId(TLeaderManagementDTO leaderManagementDTO);

    /**
     * 根据用户表ID查询其委托的领导信息(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderManagementDTO> getLeadersByConsigneeId(Long userId);

    /**
     * 根据用户表ID查询其委托的领导信息(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderManagementDTO> getAllLeadersByConsigneeId(Long userId);

    /**
     * 根据领导ID启用停用该领导
     * @param dto
     * @return
     */
    Boolean setLeaderStatus(TLeaderManagementDTO dto);

    /**
     * 修改领导管理表排序
     *
     * @param entity
     * @return
     */
    Boolean updateSort(TLeaderManagementDTO entity);

    /**
     * 获取排序号并判断是否重复
     *
     * @param sort
     * @return
     */
    Boolean getLeaderSort(Integer sort, String groupBy);
}
