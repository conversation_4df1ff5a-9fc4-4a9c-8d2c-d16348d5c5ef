package com.ctsi.hndx.schedule.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import com.ctsi.hndx.schedule.service.ITLeaderEntrustService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLeaderEntrust")
@Api(value = "领导委托信息表", tags = "领导委托信息表接口")
public class TLeaderEntrustController extends BaseController {

    private static final String ENTITY_NAME = "tLeaderEntrust";

    @Autowired
    private ITLeaderEntrustService tLeaderEntrustService;



    /**
     *  新增领导委托信息表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导委托信息表批量数据")

    public ResultVO createBatch(@RequestBody List<TLeaderEntrustDTO> tLeaderEntrustList) {
       Boolean  result = tLeaderEntrustService.insertBatch(tLeaderEntrustList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增领导委托信息表数据")
    public ResultVO<TLeaderEntrustDTO> create(@RequestBody TLeaderEntrustDTO tLeaderEntrustDTO)  {
        TLeaderEntrustDTO result = tLeaderEntrustService.create(tLeaderEntrustDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新领导委托信息表数据")
    public ResultVO update(@RequestBody TLeaderEntrustDTO tLeaderEntrustDTO) {
	    Assert.notNull(tLeaderEntrustDTO.getId(), "general.IdNotNull");
        int count = tLeaderEntrustService.update(tLeaderEntrustDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除领导委托信息表数据")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLeaderEntrustService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TLeaderEntrustDTO tLeaderEntrustDTO = tLeaderEntrustService.findOne(id);
        return ResultVO.success(tLeaderEntrustDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLeaderEntrustPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TLeaderEntrustDTO>> queryTLeaderEntrustPage(TLeaderEntrustDTO tLeaderEntrustDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLeaderEntrustService.queryListPage(tLeaderEntrustDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLeaderEntrust")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TLeaderEntrustDTO>> queryTLeaderEntrust(TLeaderEntrustDTO tLeaderEntrustDTO) {
       List<TLeaderEntrustDTO> list = tLeaderEntrustService.queryList(tLeaderEntrustDTO);
       return ResultVO.success(list);
   }

}
