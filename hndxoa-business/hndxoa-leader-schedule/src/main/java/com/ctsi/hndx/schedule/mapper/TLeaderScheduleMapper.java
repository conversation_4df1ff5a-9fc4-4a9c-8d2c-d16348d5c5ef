package com.ctsi.hndx.schedule.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.schedule.entity.TLeaderSchedule;
import com.ctsi.hndx.schedule.entity.dto.TLeaderScheduleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 领导日程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface TLeaderScheduleMapper extends MybatisBaseMapper<TLeaderSchedule> {

    /**
     * 查询根据领导ID集合查询日志
     * @param tLeaderScheduleDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<TLeaderScheduleDTO> getLeaderWeekSchedule(@Param("dto") TLeaderScheduleDTO tLeaderScheduleDTO);

    /**
     * 获取前一天的所有日程
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<TLeaderScheduleDTO> getYesterdaySchedule();



}
