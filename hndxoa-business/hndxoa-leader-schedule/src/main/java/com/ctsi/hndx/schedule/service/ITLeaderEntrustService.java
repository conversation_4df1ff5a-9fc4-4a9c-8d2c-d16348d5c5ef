package com.ctsi.hndx.schedule.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.schedule.entity.TLeaderEntrust;
import com.ctsi.hndx.schedule.entity.dto.TLeaderEntrustDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 领导委托信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ITLeaderEntrustService extends SysBaseServiceI<TLeaderEntrust> {


    /**
     * 分页查询
     */
    PageResult<TLeaderEntrustDTO> queryListPage(TLeaderEntrustDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TLeaderEntrustDTO> queryList(TLeaderEntrustDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TLeaderEntrustDTO findOne(Long id);

    /**
     * 新增
     */
    TLeaderEntrustDTO create(TLeaderEntrustDTO entity);


    /**
     * 更新
     */
    int update(TLeaderEntrustDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTLeaderEntrustId
     */
    boolean existByTLeaderEntrustId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TLeaderEntrustDTO> dataList);

    /**
     * 根据用户表ID判断该用户是否是被委托人(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    Boolean isConsignee(Long userId);

    /**
     * 根据用户表ID判断该用户是否是被委托人(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    Boolean isConsigneeInAllLeaderEntrustRel(Long userId);

    /**
     * 根据所委托人ID查询领导委托信息(排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderEntrustDTO> getLeaderEntrustByEntrustId(Long userId);

    /**
     * 根据所委托人ID查询领导委托信息(不排除被禁用的领导账号)
     * @param userId
     * @return
     */
    List<TLeaderEntrustDTO> getAllLeaderEntrustByEntrustId(Long userId);


    /**
     * 删除领导关联的委托关系
     * @param leaderId
     * @return
     */
    Boolean deleteEntrustByLeaderId(Long leaderId);

}
