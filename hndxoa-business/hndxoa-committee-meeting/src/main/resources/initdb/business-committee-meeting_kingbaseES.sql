CREATE TABLE "public"."biz_cwmeeting_inbox" (
                                                "id" int8 NOT NULL,
                                                "title" varchar(128 char) NULL,
	"title_id" int8 NULL,
	"meeting_type" varchar(32 char) NULL,
	"send_time" timestamp(6) NULL,
	"send_department_name" varchar(32 char) NULL,
	"process_instance_id" int8 NULL,
	"sender" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"receiver_id" int8 NULL,
	"receiver_name" varchar(32 char) NULL,
	"receiver_department_name" varchar(32 char) NULL,
	"receiver_mobile" varchar(32 char) NULL,
	"check_time" timestamp(6) NULL,
	"has_read" int4 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_cwmeeting_inbox_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."has_read" IS '是否已阅，0：未阅，1已阅';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."check_time" IS '查阅时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."receiver_mobile" IS '收件人联系电话';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."receiver_department_name" IS '收件人部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."receiver_name" IS '收件人名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."receiver_id" IS '收件人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."sender" IS '发送人名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."send_department_name" IS '发送部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."send_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."meeting_type" IS '类型';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."title_id" IS '公文ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."title" IS '标题';
COMMENT ON COLUMN "public"."biz_cwmeeting_inbox"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_inbox" IS '常委会会议收集箱';


CREATE TABLE "public"."biz_cwmeeting_material" (
                                                   "id" int8 NOT NULL,
                                                   "title" varchar(128 char) NULL,
	"topic_name" varchar(128 char) NULL,
	"approval" varchar(32 char) NULL,
	"meet_start_time" varchar(30 char) NULL,
	"company_name" varchar(32 char) NULL,
	"department_name" varchar(32 char) NULL,
	"create_date" date NULL,
	"urgency" varchar(32 char) NULL,
	"contents" varchar(200 char) NULL,
	"annex" varchar(200 char) NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"create_name" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_cwmeeting_material_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."create_name" IS '拟稿人';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."annex" IS '附件';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."contents" IS '正文';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."urgency" IS '缓急';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."create_date" IS '拟稿时间（年-月-日）';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."meet_start_time" IS '会议开始时间（年月日时分）';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."approval" IS '是否审批，1表示是，0表示否';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."topic_name" IS '议题名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."title" IS '标题';
COMMENT ON COLUMN "public"."biz_cwmeeting_material"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_material" IS '常委会会议材料';


CREATE TABLE "public"."biz_cwmeeting_notic" (
                                                "id" int8 NOT NULL,
                                                "title" varchar(128 char) NULL,
	"urgency" varchar(32 char) NULL,
	"approval" varchar(32 char) NULL,
	"meet_serial_number" varchar(16 char) NULL,
	"meet_start_time" varchar(30 char) NULL,
	"meet_place" varchar(255 char) NULL,
	"topic_count" int4 NULL,
	"contact_people" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"company_name" varchar(32 char) NULL,
	"department_name" varchar(32 char) NULL,
	"attendance_department_name" varchar(32 char) NULL,
	"meet_compere" varchar(255 char) NULL,
	"attendance_people" text NULL,
	"present_people" text NULL,
	"contents" varchar(200 char) NULL DEFAULT '0'::varchar,
	"annex" varchar(1 char) NULL DEFAULT '0'::varchar,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_cwmeeting_notic_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."annex" IS '是否有附件，1表示是，0表示否';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."contents" IS '正文（已生成议程的基本信息，或新建/上传的正文）';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."present_people" IS '出席人员';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."attendance_people" IS '列席人员';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."meet_compere" IS '会议主持人';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."attendance_department_name" IS '拟稿列席部门';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."department_name" IS '拟稿人部门';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."contact_people" IS '联系人';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."topic_count" IS '议题数量';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."meet_place" IS '会议地点（可查看会议室预定情况，自定义文本输入）';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."meet_start_time" IS '会议开始时间（年月日时分）';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."meet_serial_number" IS '会议序号，下拉可选择已生成议程的序号，可自定义';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."approval" IS '是否审批：1是，0否';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."urgency" IS '缓急：0加急，1平急，2特急（数据字典配置）';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."title" IS '会议标题';
COMMENT ON COLUMN "public"."biz_cwmeeting_notic"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_notic" IS '常委会会议通知';


CREATE TABLE "public"."biz_cwmeeting_screening" (
                                                    "id" int8 NOT NULL,
                                                    "title" varchar(128 char) NULL,
	"meeting_date" date NULL,
	"subjects" varchar(1000 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"notic_choose" int4 NULL DEFAULT 0,
	"report_date" date NULL,
	"mobile" varchar(32 char) NULL,
	"meet_serial_number" varchar(16 char) NULL,
	"document" varchar(1 char) NULL DEFAULT '0'::varchar,
	"duty_people" varchar(32 char) NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"department_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"deleted" int4 NULL DEFAULT 0,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_name" varchar(50 char) NULL,
	CONSTRAINT "biz_cwmeeting_screening_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."department_name" IS '拟稿部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."create_time" IS '创建时间（拟稿时间）';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."bpm_status" IS '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."duty_people" IS '负责人';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."document" IS '是否有正文';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."meet_serial_number" IS '会议序号（限4位数，0001-9999）';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."mobile" IS '拟稿人电话';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."report_date" IS '申报日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."notic_choose" IS '是否会议通知，1表示选择，其他不选择';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."company_id" IS '拟稿单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."create_name" IS '拟稿人';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."subjects" IS '选择议题';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."meeting_date" IS '会议时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."title" IS '议程标题';
COMMENT ON COLUMN "public"."biz_cwmeeting_screening"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_screening" IS '常委会议题筛选';


CREATE TABLE "public"."biz_cwmeeting_summary" (
                                                  "id" int8 NOT NULL,
                                                  "title" varchar(128 char) NULL,
	"approval" varchar(32 char) NULL,
	"meet_serial_number" varchar(16 char) NULL,
	"company_name" varchar(32 char) NULL,
	"department_name" varchar(32 char) NULL,
	"create_date" date NULL,
	"urgency" varchar(32 char) NULL,
	"contents" varchar(200 char) NULL,
	"annex" varchar(200 char) NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"create_name" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_cwmeeting_summary_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."create_name" IS '拟稿人';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."annex" IS '附件';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."contents" IS '正文';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."urgency" IS '缓急';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."create_date" IS '拟稿时间（年月日）';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."meet_serial_number" IS '会议序号，下拉可选择已生成议程的序号，可自定义';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."approval" IS '是否审批：1表示是，0表示否';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."title" IS '标题';
COMMENT ON COLUMN "public"."biz_cwmeeting_summary"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_summary" IS '常委会会议纪要';


CREATE TABLE "public"."biz_cwmeeting_topic" (
                                                "id" int8 NOT NULL,
                                                "subject_name" varchar(300 char) NULL,
	"report_company_name" varchar(32 char) NULL,
	"has_meeting_material" varchar(1 char) NULL DEFAULT '0'::varchar,
	"raise_subject_leader" varchar(32 char) NULL,
	"report_people_name" varchar(1000 char) NULL,
	"report_people_duty" varchar(1000 char) NULL,
	"cost_time_minute" int4 NULL,
	"contact_people" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"report_date" date NULL,
	"major_problem" varchar(1000 char) NULL,
	"is_approval" varchar(1 char) NULL DEFAULT '0'::varchar,
	"is_news_declare" varchar(1 char) NULL DEFAULT '0'::varchar,
	"attend_leader" varchar(50 char) NULL,
	"attend_deapart_name" varchar(50 char) NULL,
	"contents" varchar(200 char) NULL DEFAULT '0'::varchar,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"up_meet" varchar(1 char) NULL DEFAULT '0'::varchar,
	"submit_time" varchar(30 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(50 char) NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp(6) NULL,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	CONSTRAINT "biz_cwmeeting_topic_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."department_name" IS '拟稿部门名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."submit_time" IS '提交到议题库的时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."up_meet" IS '0没有提交议题库,1提交议题库没有上会，2已上会';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."bpm_status" IS '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."contents" IS '正文';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."attend_deapart_name" IS '拟列席部门';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."attend_leader" IS '拟列席市领导';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."is_news_declare" IS '是否进行新闻报道，1表示是，0表示否（默认否)';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."is_approval" IS '是否审批，1表示是，0表示否（默认否，不走审批流程，提交后直接进入议题库)';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."major_problem" IS '提请解决的主要问题';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."report_date" IS '申报日期';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."contact_people" IS '联系人';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."cost_time_minute" IS '议题所需时间';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."report_people_duty" IS '汇报人职务';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."report_people_name" IS '汇报人员的姓名，多个逗号隔开';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."raise_subject_leader" IS '提出议题常委领导';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."has_meeting_material" IS '1表示有会议材料，0表示没有';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."report_company_name" IS '汇报单位名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."subject_name" IS '议题名称';
COMMENT ON COLUMN "public"."biz_cwmeeting_topic"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_cwmeeting_topic" IS '常委会议题申报';
