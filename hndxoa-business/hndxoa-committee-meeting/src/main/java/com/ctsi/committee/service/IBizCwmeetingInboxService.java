package com.ctsi.committee.service;

import com.ctsi.committee.entity.BizCwmeetingInbox;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxDistributeDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxParamDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxResultDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 常委会会议收件箱 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
public interface IBizCwmeetingInboxService extends SysBaseServiceI<BizCwmeetingInbox> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizCwmeetingInboxResultDTO> queryListPage(BizCwmeetingInboxParamDTO entityDTO, BasePageForm page);

    /**
     * 查看分发
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizCwmeetingInboxDistributeDTO> queryDirtributePage(BizCwmeetingInbox entityDTO, BasePageForm page);

    /**
     * 保存分发的公文
     *
     * @param entityDTOs
     * @return
     */
    int createDistributeCwmeeting(List<BizCwmeetingInbox> entityDTOs);

    /**
     * 更新
     *
     * @param id
     * @return
     */
    int update(Long id,String meetingType);

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    int delete(Long id);
}
