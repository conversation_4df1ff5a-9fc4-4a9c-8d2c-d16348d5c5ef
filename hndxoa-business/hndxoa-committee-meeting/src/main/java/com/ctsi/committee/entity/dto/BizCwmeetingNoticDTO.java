package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 常委会会议通知
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingNoticDTO对象", description="常委会会议通知")
public class BizCwmeetingNoticDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "会议标题")
    private String title;

    /**
     * 缓急（数据字典配置）
     */
    @ApiModelProperty(value = "缓急（数据字典配置）")
    private String urgency;

    /**
     * 是否审批：是，否
     */
    @ApiModelProperty(value = "是否审批：是，否")
    private String approval;

    /**
     * 会议序号，下拉可选择已生成议程的序号，可自定义
     */
    @ApiModelProperty(value = "会议序号，下拉可选择已生成议程的序号，可自定义")
    private String meetSerialNumber;

    /**
     * 会议开始时间（年月日时分）
     */
    @ApiModelProperty(value = "会议开始时间（年月日时分）")
    private String meetStartTime;

    /**
     * 拟稿时间（年月日时分）
     */
    @ApiModelProperty(value = "拟稿时间（年月日时分）")
    private LocalDateTime createTime;

    /**
     * 会议地点（可查看会议室预定情况，自定义文本输入）
     */
    @ApiModelProperty(value = "会议地点（可查看会议室预定情况，自定义文本输入）")
    private String meetPlace;

    /**
     * 议题数量
     */
    @ApiModelProperty(value = "议题数量")
    private Integer topicCount;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPeople;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 拟稿单位
     */
    @ApiModelProperty(value = "拟稿单位")
    private String companyName;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 会议主持人
     */
    @ApiModelProperty(value = "会议主持人")
    private String meetCompere;

    /**
     * 列席人员
     */
    @ApiModelProperty(value = "列席人员")
    private String attendancePeople;

    /**
     * 出席人员
     */
    @ApiModelProperty(value = "出席人员")
    private String presentPeople;

    /**
     * 正文（已生成议程的基本信息，或新建/上传的正文）
     */
    @ApiModelProperty(value = "正文（已生成议程的基本信息，或新建/上传的正文）")
    private String contents;

    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;
}
