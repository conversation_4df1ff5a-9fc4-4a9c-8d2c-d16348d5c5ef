package com.ctsi.committee.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 常委会会议收件箱
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingInbox对象", description="常委会会议收件箱")
public class BizCwmeetingInbox extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 是否查阅
     */
    @ApiModelProperty(value = "是否查阅，0表示未查阅，1表示已查阅")
    private Integer hasRead;

    /**
     * 会议标题id
     */
    @ApiModelProperty(value = "会议标题id")
    private Long titleId;

    @ApiModelProperty("类型")
    private String meetingType;

    @ApiModelProperty("发送时间，年月日时分秒")
    private LocalDateTime sendTime;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;

    /**
     * 收件人ID
     */
    @ApiModelProperty(value = "收件人ID")
    private Long receiverId;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String receiverName;

    /**
     * 收件人部门名称
     */
    @ApiModelProperty(value = "收件人部门名称")
    private String receiverDepartmentName;

    /**
     * 收件人联系电话
     */
    @ApiModelProperty(value = "收件人联系电话")
    private String receiverMobile;

    /**
     * 查阅时间
     */
    @ApiModelProperty(value = "查阅时间")
    private LocalDateTime checkTime;

    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    private String sendDepartmentName;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;
}
