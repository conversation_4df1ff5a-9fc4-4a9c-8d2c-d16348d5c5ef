package com.ctsi.committee.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 常委会会议议题
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_cwmeeting_topic")
@ApiModel(value="BizCwmeetingTopic对象", description="常委会会议议题")
public class BizCwmeetingTopic extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 议题名称
     */
    @ApiModelProperty(value = "议题名称")
    private String subjectName;

    /**
     * 汇报单位名称
     */
    @ApiModelProperty(value = "汇报单位名称")
    private String reportCompanyName;

    /**
     * 1表示有会议材料，0表示没有
     */
    @ApiModelProperty(value = "1表示有会议材料，0表示没有")
    private String hasMeetingMaterial;

    /**
     * 提出议题常委领导
     */
    @ApiModelProperty(value = "提出议题常委领导")
    private String raiseSubjectLeader;

    /**
     * 汇报人员的姓名，多个逗号隔开
     */
    @ApiModelProperty(value = "汇报人员的姓名，多个逗号隔开")
    private String reportPeopleName;

    /**
     * 汇报人职务
     */
    @ApiModelProperty(value = "汇报人职务")
    private String reportPeopleDuty;

    /**
     * 议题所需时间
     */
    @ApiModelProperty(value = "议题所需时间")
    private Integer costTimeMinute;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPeople;

    /**
     * 申报日期（年月日）
     */
    @ApiModelProperty(value = "申报日期（年月日）")
    private LocalDate reportDate;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 提请解决的主要问题
     */
    @ApiModelProperty(value = "提请解决的主要问题")
    private String majorProblem;

    /**
     * 是否审批，1表示是，0表示否（默认否，不走审批流程，提交后直接进入议题库)
     */
    @ApiModelProperty(value = "是否审批，1表示是，0表示否（默认否，不走审批流程，提交后直接进入议题库)")
    private String isApproval;

    /**
     * 是否进行新闻报道，1表示是，0表示否（默认否)
     */
    @ApiModelProperty(value = "是否进行新闻报道，1表示是，0表示否（默认否)")
    private String isNewsDeclare;

    /**
     * 拟列席市领导
     */
    @ApiModelProperty(value = "拟列席市领导")
    private String attendLeader;

    /**
     * 拟列席部门
     */
    @ApiModelProperty(value = "拟列席部门")
    private String attendDeapartName;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 正文
     */
    @ApiModelProperty(value = "正文")
    private String contents;

    /**
     * 0 表示没有提交议题库,1表示提交议题库没有上会，2表示已上会
     */
    @ApiModelProperty(value = "0 表示没有提交议题库,1表示提交议题库没有上会，2表示已上会")
    private String upMeet;

    @ApiModelProperty(value = "提交到议题库的时间")
    private LocalDateTime submitTime;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;
}
