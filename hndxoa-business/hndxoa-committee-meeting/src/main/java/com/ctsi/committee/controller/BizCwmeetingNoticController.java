package com.ctsi.committee.controller;
import com.ctsi.committee.entity.dto.BizCwmeetingNoticDTO;
import com.ctsi.committee.service.IBizCwmeetingNoticService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizCwmeetingNotic")
@Api(value = "常委会会议通知", tags = "常委会会议通知接口")
public class BizCwmeetingNoticController extends BaseController {

    private static final String ENTITY_NAME = "bizCwmeetingNotic";

    @Autowired
    private IBizCwmeetingNoticService bizCwmeetingNoticService;



    /**
     *  新增常委会会议通知批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizCwmeetingNotic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增常委会会议通知批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingNotic.add')")
    public ResultVO createBatch(@RequestBody List<BizCwmeetingNoticDTO> bizCwmeetingNoticList) {
       Boolean  result = bizCwmeetingNoticService.insertBatch(bizCwmeetingNoticList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizCwmeetingNotic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增常委会会议通知数据")
   // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingNotic.add')")
    public ResultVO<BizCwmeetingNoticDTO> create(@RequestBody BizCwmeetingNoticDTO bizCwmeetingNoticDTO)  {
        BizCwmeetingNoticDTO result = bizCwmeetingNoticService.create(bizCwmeetingNoticDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizCwmeetingNotic.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新常委会会议通知数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingNotic.update')")
    public ResultVO update(@RequestBody BizCwmeetingNoticDTO bizCwmeetingNoticDTO) {
	    Assert.notNull(bizCwmeetingNoticDTO.getId(), "general.IdNotNull");
        int count = bizCwmeetingNoticService.update(bizCwmeetingNoticDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除常委会会议通知数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizCwmeetingNotic.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingNotic.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizCwmeetingNoticService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizCwmeetingNoticDTO bizCwmeetingNoticDTO = bizCwmeetingNoticService.findOne(id);
        return ResultVO.success(bizCwmeetingNoticDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizCwmeetingNoticPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizCwmeetingNoticDTO>> queryBizCwmeetingNoticPage(BizCwmeetingNoticDTO bizCwmeetingNoticDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizCwmeetingNoticService.queryListPage(bizCwmeetingNoticDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizCwmeetingNotic")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizCwmeetingNoticDTO>> queryBizCwmeetingNotic(BizCwmeetingNoticDTO bizCwmeetingNoticDTO) {
       List<BizCwmeetingNoticDTO> list = bizCwmeetingNoticService.queryList(bizCwmeetingNoticDTO);
       return ResultVO.success(new ResResult<BizCwmeetingNoticDTO>(list));
   }

}
