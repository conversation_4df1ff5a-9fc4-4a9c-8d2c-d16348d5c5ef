package com.ctsi.committee.controller;
import com.ctsi.committee.entity.dto.BizCwmeetingTopicDTO;
import com.ctsi.committee.service.IBizCwmeetingTopicService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizCwmeetingTopic")
@Api(value = "常委会议题", tags = "常委会议题接口")
public class BizCwmeetingTopicController extends BaseController {

    private static final String ENTITY_NAME = "bizCwmeetingTopic";

    @Autowired
    private IBizCwmeetingTopicService bizCwmeetingTopicService;



    /**
     *  新增常委会议题申报批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizCwmeetingTopic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增常委会议-议题申报批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingTopic.add')")
    public ResultVO createBatch(@RequestBody List<BizCwmeetingTopicDTO> bizCwmeetingTopicList) {
       Boolean  result = bizCwmeetingTopicService.insertBatch(bizCwmeetingTopicList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizCwmeetingTopic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增常委会议-议题申报数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingTopic.add')")
    public ResultVO<BizCwmeetingTopicDTO> create(@RequestBody BizCwmeetingTopicDTO bizCwmeetingTopicDTO)  {
        BizCwmeetingTopicDTO result = bizCwmeetingTopicService.create(bizCwmeetingTopicDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizCwmeetingTopic.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新常委会议-议题申报数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingTopic.update')")
    public ResultVO update(@RequestBody BizCwmeetingTopicDTO bizCwmeetingTopicDTO) {
	    Assert.notNull(bizCwmeetingTopicDTO.getId(), "general.IdNotNull");
        int count = bizCwmeetingTopicService.update(bizCwmeetingTopicDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  议题上报，提交议题到议题库
     */
    @PostMapping("/submitTopic/{id}")
    @ApiOperation(value = "议题上报", notes = "传入参数")
    // @OperationLog(dBOperation = DBOperation.UPDATE,message = "议题上报")
    public ResultVO submitTopic(@PathVariable Long id) {
        int count = bizCwmeetingTopicService.submitTopic(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  议题库提交已上会
     */
    @PostMapping("/topicUpMeet/{id}")
    @ApiOperation(value = "议题上会", notes = "传入参数")
    // @OperationLog(dBOperation = DBOperation.UPDATE,message = "议题上会")
    public ResultVO topicUpMeet(@PathVariable Long id) {
        int count = bizCwmeetingTopicService.topicUpMeet(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除常委会议-议题申报数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizCwmeetingTopic.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingTopic.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizCwmeetingTopicService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizCwmeetingTopicDTO bizCwmeetingTopicDTO = bizCwmeetingTopicService.findOne(id);
        return ResultVO.success(bizCwmeetingTopicDTO);
    }

    /**
    *  分页查询多条数据.议题库未上会、已上会
    */
    @GetMapping("/queryBizCwmeetingTopicPage")
    @ApiOperation(value = "议题库（未上会、已上会）分页查询多条数据，0表示没有提交议题库，1表示提交议题库没有上会，2表示已上会", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizCwmeetingTopicDTO>> queryBizCwmeetingTopicPage(BizCwmeetingTopicDTO bizCwmeetingTopicDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizCwmeetingTopicService.queryListPage(bizCwmeetingTopicDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizCwmeetingTopic")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizCwmeetingTopicDTO>> queryBizCwmeetingTopic(BizCwmeetingTopicDTO bizCwmeetingTopicDTO) {
       List<BizCwmeetingTopicDTO> list = bizCwmeetingTopicService.queryList(bizCwmeetingTopicDTO);
       return ResultVO.success(new ResResult<BizCwmeetingTopicDTO>(list));
   }

}
