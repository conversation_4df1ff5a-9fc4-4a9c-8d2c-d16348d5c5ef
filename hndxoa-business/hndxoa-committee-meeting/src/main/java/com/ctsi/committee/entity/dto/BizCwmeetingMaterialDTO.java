package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 常委会会议材料
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingMaterialDTO对象", description="常委会会议材料")
public class BizCwmeetingMaterialDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 议题名称
     */
    @ApiModelProperty(value = "议题名称")
    private String topicName;

    /**
     * 是否审批
     */
    @ApiModelProperty(value = "是否审批")
    private String approval;

    /**
     * 会议开始时间（年月日时分）
     */
    @ApiModelProperty(value = "会议开始时间（年月日时分）")
    private LocalDateTime meetStartTime;

    /**
     * 拟稿单位
     */
    @ApiModelProperty(value = "拟稿单位")
    private String companyName;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "拟稿人部门名称")
    private String departmentName;

    /**
     * 拟稿时间（年-月-日）
     */
    @ApiModelProperty(value = "拟稿时间（年-月-日）")
    private LocalDate createDate;

    /**
     * 缓急
     */
    @ApiModelProperty(value = "缓急")
    private String urgency;

    /**
     * 正文
     */
    @ApiModelProperty(value = "正文")
    private String contents;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;


}
