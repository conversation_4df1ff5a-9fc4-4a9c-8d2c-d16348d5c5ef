package com.ctsi.committee.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.committee.entity.BizCwmeetingInbox;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxDistributeDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxParamDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxResultDTO;
import com.ctsi.committee.mapper.BizCwmeetingInboxMapper;
import com.ctsi.committee.service.IBizCwmeetingInboxService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 常委会会议收件箱 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Slf4j
@Service
public class BizCwmeetingInboxServiceImpl extends SysBaseServiceImpl<BizCwmeetingInboxMapper, BizCwmeetingInbox> implements IBizCwmeetingInboxService {

    @Autowired
    private BizCwmeetingInboxMapper bizCwmeetingInboxMapper;

    /**
     * 1 表示收件箱公文已查阅
     */
    private static final Integer READED = 1;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizCwmeetingInboxResultDTO> queryListPage(BizCwmeetingInboxParamDTO entityDTO, BasePageForm page) {

        LambdaQueryWrapper<BizCwmeetingInbox> queryWrapper = new LambdaQueryWrapper<>();
        // 收件箱公文，0 表示未查阅，1 表示已查阅
        queryWrapper.eq(BizCwmeetingInbox::getHasRead, entityDTO.getHasRead());
        queryWrapper.eq(BizCwmeetingInbox::getReceiverId, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getTitle()), BizCwmeetingInbox::getTitle, entityDTO.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getSender()), BizCwmeetingInbox::getCreateName, entityDTO.getSender());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getSendDepartmentName()), BizCwmeetingInbox::getSendDepartmentName, entityDTO.getSendDepartmentName());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizCwmeetingInbox::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizCwmeetingInbox::getCreateTime, entityDTO.getEndTime());
        queryWrapper.orderByDesc(BizCwmeetingInbox::getSendTime);
        IPage<BizCwmeetingInbox> data = bizCwmeetingInboxMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        IPage<BizCwmeetingInboxResultDTO> pageData = data.convert(entity -> BeanConvertUtils.copyProperties(entity, BizCwmeetingInboxResultDTO.class));
        return new PageResult<BizCwmeetingInboxResultDTO>(pageData.getRecords(), pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 查看分发
     *
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizCwmeetingInboxDistributeDTO> queryDirtributePage(BizCwmeetingInbox entityDTO, BasePageForm page) {
        LambdaQueryWrapper<BizCwmeetingInbox> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(entityDTO.getTitleId()!=null, BizCwmeetingInbox::getTitleId, entityDTO.getTitleId());
        queryWrapper.eq(BizCwmeetingInbox::getCreateBy, entityDTO.getCreateBy());
        queryWrapper.orderByDesc(BizCwmeetingInbox::getSendTime);
        IPage<BizCwmeetingInbox> data = bizCwmeetingInboxMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);

        List<BizCwmeetingInboxDistributeDTO> collect = data.getRecords().stream().map(entity ->{
            BizCwmeetingInboxDistributeDTO bizCwmeetingInboxDistributeDTO = new BizCwmeetingInboxDistributeDTO();
            bizCwmeetingInboxDistributeDTO.setCreateTime(entity.getCreateTime());
            // 收件箱中0表示未阅，1表示已阅。与分发中的状态值相反。
            bizCwmeetingInboxDistributeDTO.setReadNotRead(entity.getHasRead()==0?"1":"0");
            bizCwmeetingInboxDistributeDTO.setTitle(entity.getTitle());
            bizCwmeetingInboxDistributeDTO.setUserId(entity.getCreateBy());
            bizCwmeetingInboxDistributeDTO.setUserName(entity.getReceiverName());
            // cwmeeting_inbox 表的id
            bizCwmeetingInboxDistributeDTO.setId(entity.getId());
            return bizCwmeetingInboxDistributeDTO;
        }).collect(Collectors.toList());

        return new PageResult<BizCwmeetingInboxDistributeDTO>(collect, data.getTotal(), data.getCurrent());
    }

    /**
     * 保存分发的公文
     *
     * @param entityDTOs
     * @return
     */
    @Override
    public int createDistributeCwmeeting(List<BizCwmeetingInbox> entityDTOs) {
        //剔除已经分发过的用户
        //查询通过选择的用户到分发用户表中匹配
        List<Long> distributeReceiveUserList = bizCwmeetingInboxMapper.selectList(
                new LambdaQueryWrapper<BizCwmeetingInbox>()
                        .in(BizCwmeetingInbox::getReceiverId, entityDTOs.stream().map(i -> i.getReceiverId()).collect(Collectors.toList()))
                        .eq(BizCwmeetingInbox::getTitleId, entityDTOs.get(0).getTitleId())
                        .eq(BizCwmeetingInbox::getMeetingType, entityDTOs.get(0).getMeetingType())
        ).stream().map(i -> i.getReceiverId()).collect(Collectors.toList());

        // 过滤已分发的用户
        List<BizCwmeetingInbox> collect = entityDTOs.stream().filter(entity -> {
            if (!distributeReceiveUserList.contains(entity.getReceiverId())) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        boolean b = saveBatch(collect);
        return b ? 1 : 0;
    }


    /**
     * 浏览公文，更新公文查阅状态
     *
     * @param id,meetingType the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(Long id, String meetingType) {

        LambdaUpdateWrapper<BizCwmeetingInbox> updateWrapper = new LambdaUpdateWrapper<>();
        // 设置该条分发公文已读
        updateWrapper.set(BizCwmeetingInbox::getHasRead, READED);
        // 设置分发查阅时间为当前时间
        updateWrapper.set(BizCwmeetingInbox::getCheckTime, LocalDateTimeUtil.now());
        updateWrapper.eq(BizCwmeetingInbox::getId, id);
        return bizCwmeetingInboxMapper.update(null, updateWrapper);
    }


    /**
     * 删除，
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizCwmeetingInboxMapper.deleteById(id);
    }
}
