package com.ctsi.committee.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.committee.entity.BizCwmeetingTopic;
import com.ctsi.committee.entity.dto.BizCwmeetingTopicDTO;
import com.ctsi.committee.mapper.BizCwmeetingTopicMapper;
import com.ctsi.committee.service.IBizCwmeetingTopicService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.BpmStatusConstants;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 常委会议题 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Slf4j
@Service
public class BizCwmeetingTopicServiceImpl extends SysBaseServiceImpl<BizCwmeetingTopicMapper, BizCwmeetingTopic> implements IBizCwmeetingTopicService {

    @Autowired
    private BizCwmeetingTopicMapper bizCwmeetingTopicMapper;

    /**
     * 没有提交议题库
     */
    private final static String NO_SUBMIT = "0";

    /**
     * 提交议题库没有上会
     */
    private final static String SUBMIT = "1";


    /**
     * 提交议题库并且上会
     */
    private final static String MEET = "2";

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizCwmeetingTopicDTO> queryListPage(BizCwmeetingTopicDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizCwmeetingTopic> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getSubjectName()), BizCwmeetingTopic::getSubjectName, entityDTO.getSubjectName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getReportCompanyName()), BizCwmeetingTopic::getReportCompanyName, entityDTO.getReportCompanyName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getReportPeopleName()), BizCwmeetingTopic::getReportPeopleName, entityDTO.getReportPeopleName());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizCwmeetingTopic::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizCwmeetingTopic::getCreateTime, entityDTO.getEndTime());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getUpMeet()), BizCwmeetingTopic::getUpMeet, entityDTO.getUpMeet());
        queryWrapper.orderByDesc(BizCwmeetingTopic::getCreateTime);

        IPage<BizCwmeetingTopic> pageData = bizCwmeetingTopicMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizCwmeetingTopicDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizCwmeetingTopicDTO.class));
        List<BizCwmeetingTopicDTO> tMeetingTopicDTOS = data.getRecords();
        if (CollectionUtil.isNotEmpty(tMeetingTopicDTOS)) {
            tMeetingTopicDTOS.forEach(tMeetingTopicDTO -> {
                String reportPeopleName = tMeetingTopicDTO.getReportPeopleName();
                if (StringUtils.isNotEmpty(reportPeopleName) && reportPeopleName.startsWith("[{")) {
                    JSONArray jsonArray = JSONArray.fromObject(reportPeopleName);
                    if (jsonArray.size() > 0) {
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        if (jsonObject.containsKey("userName")) {
                            String realName = jsonObject.getString("userName");
                            tMeetingTopicDTO.setReportPeopleName(realName);
                        }
                    }
                }
            });
        }
        return new PageResult<BizCwmeetingTopicDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizCwmeetingTopicDTO> queryList(BizCwmeetingTopicDTO entityDTO) {
        LambdaQueryWrapper<BizCwmeetingTopic> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(entityDTO.getBpmStatus()!=null,BizCwmeetingTopic::getBpmStatus, entityDTO.getBpmStatus());
        List<BizCwmeetingTopic> listData = bizCwmeetingTopicMapper.selectList(queryWrapper);
        List<BizCwmeetingTopicDTO> BizCwmeetingTopicDTOList = ListCopyUtil.copy(listData, BizCwmeetingTopicDTO.class);
        return BizCwmeetingTopicDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizCwmeetingTopicDTO findOne(Long id) {
        BizCwmeetingTopic bizCwmeetingTopic = bizCwmeetingTopicMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizCwmeetingTopic, BizCwmeetingTopicDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizCwmeetingTopicDTO create(BizCwmeetingTopicDTO entityDTO) {
        BizCwmeetingTopic bizCwmeetingTopic = BeanConvertUtils.copyProperties(entityDTO, BizCwmeetingTopic.class);
        save(bizCwmeetingTopic);
        return BeanConvertUtils.copyProperties(bizCwmeetingTopic, BizCwmeetingTopicDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizCwmeetingTopicDTO entity) {
        BizCwmeetingTopic bizCwmeetingTopic = BeanConvertUtils.copyProperties(entity, BizCwmeetingTopic.class);
        return bizCwmeetingTopicMapper.updateById(bizCwmeetingTopic);
    }

    /**
     * 议题上报
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submitTopic(Long id) {
        BizCwmeetingTopic bizCwmeetingTopic = bizCwmeetingTopicMapper.selectById(id);
        if (bizCwmeetingTopic != null && BpmStatusConstants.PROCESS_END != bizCwmeetingTopic.getBpmStatus()) {
            throw new BusinessException("没有走完流程的议题不可以提交");
        }
        bizCwmeetingTopic.setUpMeet(SUBMIT);
        bizCwmeetingTopic.setSubmitTime(LocalDateTime.now());
        return bizCwmeetingTopicMapper.updateById(bizCwmeetingTopic);
    }

    /**
     * 上会
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int topicUpMeet(Long id) {
        BizCwmeetingTopic bizCwmeetingTopic = bizCwmeetingTopicMapper.selectById(id);
        if (bizCwmeetingTopic != null && BpmStatusConstants.PROCESS_END != bizCwmeetingTopic.getBpmStatus()) {
            throw new BusinessException("没有走完流程的议题不可以提交");
        }
        bizCwmeetingTopic.setUpMeet(MEET);
        return bizCwmeetingTopicMapper.updateById(bizCwmeetingTopic);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizCwmeetingTopicMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizCwmeetingTopicId
     * @return
     */
    @Override
    public boolean existByBizCwmeetingTopicId(Long BizCwmeetingTopicId) {
        if (BizCwmeetingTopicId != null) {
            LambdaQueryWrapper<BizCwmeetingTopic> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizCwmeetingTopic::getId, BizCwmeetingTopicId);
            List<BizCwmeetingTopic> result = bizCwmeetingTopicMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizCwmeetingTopicDTO> dataList) {
        List<BizCwmeetingTopic> result = ListCopyUtil.copy(dataList, BizCwmeetingTopic.class);
        return saveBatch(result);
    }


}
