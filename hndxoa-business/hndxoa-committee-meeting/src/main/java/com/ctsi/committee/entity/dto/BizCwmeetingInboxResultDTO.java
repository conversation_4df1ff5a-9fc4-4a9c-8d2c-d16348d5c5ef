package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 常委会会议收件箱
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingInboxResultDTO对象", description="常委会会议收件箱")
public class BizCwmeetingInboxResultDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty("类型")
    private String meetingType;

    @ApiModelProperty("发送时间，年月日时分秒")
    private LocalDateTime sendTime;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;

    /**
     * 收件人部门名称
     */
    @ApiModelProperty(value = "收件人部门名称")
    private String receiverDepartmentName;

    /**
     * 收件人联系电话
     */
    @ApiModelProperty(value = "收件人联系电话")
    private String receiverMobile;

    /**
     * 查阅时间
     */
    @ApiModelProperty(value = "查阅时间")
    private LocalDateTime checkTime;

    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    private String sendDepartmentName;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;
}
