package com.ctsi.committee.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 常委会议题筛选
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_cwmeeting_screening")
@ApiModel(value = "BizCwmeetingScreening对象", description = "常委会议题筛选")
public class BizCwmeetingScreening extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 选择议题
     */
    @ApiModelProperty(value = "议程标题")
    private String title;

    @ApiModelProperty(value = "会议日期")
    private LocalDate meetingDate;

    /**
     * 选择议题
     */
    @ApiModelProperty(value = "选择议题 合集")
    private String subjects;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String companyName;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 是否会议通知，1表示选择，其他不选择
     */
    @ApiModelProperty(value = "是否会议通知，1表示选择，其他不选择")
    private Integer noticChoose;

    /**
     * 拟稿人电话
     */
    @ApiModelProperty(value = "拟稿人电话")
    private String mobile;

    /**
     * 申报日期（年月日）
     */
    @ApiModelProperty(value = "申报日期（年月日）")
    private LocalDate reportDate;

    /**
     * 会议序号（限4位数，0001-9999）
     */
    @ApiModelProperty(value = "会议序号（限4位数，0001-9999）")
    private String meetSerialNumber;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String dutyPeople;

    /**
     * 是否有正文
     */
    @ApiModelProperty(value = "是否有正文")
    private String document;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;

}
