package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 常委会会议收件箱分发
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingInboxDistributeDTO对象", description="常委会会议收件箱分发")
public class BizCwmeetingInboxDistributeDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 是否已阅（0：已阅 1：未阅）
     */
    @ApiModelProperty(value = "是否已阅（0：已阅 1：未阅）")
    private String readNotRead;

    /**
     * 处理人id
     */
    @ApiModelProperty(value = "处理人id")
    private Long userId;


    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private String userName;
}
