package com.ctsi.committee.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctsi.committee.entity.dto.BizCwmeetingScreeningDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingScreeningTopicDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingTopicDTO;
import com.ctsi.committee.service.IBizCwmeetingScreeningService;
import com.ctsi.committee.service.IBizCwmeetingTopicService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.FormUrlData;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.ExportData;
import com.ctsi.hndx.utils.FileNameUtil;
import com.ctsi.hndx.utils.WordUtil;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizCwmeetingScreening")
@Api(value = "常委会议程编辑", tags = "常委会议程编辑接口")
public class BizCwmeetingScreeningController extends BaseController {

    private static final String ENTITY_NAME = "bizCwmeetingScreening";

    @Autowired
    private IBizCwmeetingScreeningService bizCwmeetingScreeningService;

    @Autowired
    private IBizCwmeetingTopicService bizCwmeetingTopicService;


    /**
     * 新增常委会议题筛选批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizCwmeetingScreening.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增常委会议题筛选批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingScreening.add')")
    public ResultVO createBatch(@RequestBody List<BizCwmeetingScreeningDTO> bizCwmeetingScreeningList) {
        Boolean result = bizCwmeetingScreeningService.insertBatch(bizCwmeetingScreeningList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizCwmeetingScreening.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增常委会议题筛选数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingScreening.add')")
    public ResultVO<BizCwmeetingScreeningDTO> create(@RequestBody BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO) {
        BizCwmeetingScreeningDTO result = bizCwmeetingScreeningService.create(bizCwmeetingScreeningDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizCwmeetingScreening.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新常委会议题筛选数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingScreening.update')")
    public ResultVO update(@RequestBody BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO) {
        Assert.notNull(bizCwmeetingScreeningDTO.getId(), "general.IdNotNull");
        int count = bizCwmeetingScreeningService.update(bizCwmeetingScreeningDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除常委会议题筛选数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizCwmeetingScreening.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingScreening.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizCwmeetingScreeningService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO = bizCwmeetingScreeningService.findOne(id);
        return ResultVO.success(bizCwmeetingScreeningDTO);
    }

    /**
     * 表单中用来查询所有议题筛选流程已结束的会议序号
     */
    @GetMapping("/queryCwMeetSerialNumber")
    @ApiOperation(value = "表单中用来查询所有议题筛选流程已结束的会议序号", notes = "无入参数")
    public ResultVO<List<FormUrlData>> queryBizCwMeetSerialNumber() {
        BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO = new BizCwmeetingScreeningDTO();
        // 只查询议题筛选流程已结束的会议议程，3表示流程已结束
        bizCwmeetingScreeningDTO.setBpmStatus(3);
        List<BizCwmeetingScreeningDTO> list = bizCwmeetingScreeningService.queryList(bizCwmeetingScreeningDTO);
        List<FormUrlData> formUrlDataList = new ArrayList<>();
        list.forEach(bizCwmeetingScreeningDTO1 -> {
            FormUrlData formUrlData = new FormUrlData();
            formUrlData.setLabel(bizCwmeetingScreeningDTO1.getMeetSerialNumber());
            formUrlData.setValue(String.valueOf(bizCwmeetingScreeningDTO1.getId()));
            formUrlDataList.add(formUrlData);
        });
        return ResultVO.success(formUrlDataList);
    }

    /**
     * 表单中用来查询议题库中所有未上会的议题名称
     */
    @GetMapping("/queryCwMeetTopics")
    @ApiOperation(value = "表单中用来查询议题库中所有未上会的议题名称", notes = "无入参数")
    public ResultVO<List<FormUrlData>> queryBizCwMeetTopics() {
        BizCwmeetingTopicDTO bizCwmeetingTopicDTO = new BizCwmeetingTopicDTO();
        // 只查询议题库中所有未上会的议题名称，SUBMIT_NO_MEET=1 表示未上会的议题
        bizCwmeetingTopicDTO.setBpmStatus(BizCwmeetingScreeningDTO.SUBMIT_NO_MEET);
        List<BizCwmeetingTopicDTO> list = bizCwmeetingTopicService.queryList(bizCwmeetingTopicDTO);
        List<FormUrlData> formUrlDataList = new ArrayList<>();
        list.forEach(bizCwmeetingScreeningDTO1 -> {
            FormUrlData formUrlData = new FormUrlData();
            formUrlData.setLabel(bizCwmeetingScreeningDTO1.getSubjectName());
            formUrlData.setValue(String.valueOf(bizCwmeetingScreeningDTO1.getId()));
            formUrlDataList.add(formUrlData);
        });
        return ResultVO.success(formUrlDataList);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizCwmeetingScreeningPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizCwmeetingScreeningDTO>> queryBizCwmeetingScreeningPage(BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizCwmeetingScreeningService.queryListPage(bizCwmeetingScreeningDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizCwmeetingScreening")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizCwmeetingScreeningDTO>> queryBizCwmeetingScreening(BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO) {
        List<BizCwmeetingScreeningDTO> list = bizCwmeetingScreeningService.queryList(bizCwmeetingScreeningDTO);
        return ResultVO.success(new ResResult<BizCwmeetingScreeningDTO>(list));
    }

    /**
     * 导出议程信息
     */
    @GetMapping("/downBizCwmeetAgenda/{id}")
    @ApiOperation(value = "导出议程接口", notes = "传入参数")
    public void downMeetAgenda(@PathVariable Long id, HttpServletResponse response) {
        BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO = bizCwmeetingScreeningService.findOne(id);
        String title = bizCwmeetingScreeningDTO.getTitle();
        String agentDate = LocalDateTimeUtil.format(bizCwmeetingScreeningDTO.getMeetingDate(), DatePattern.CHINESE_DATE_PATTERN);
        List<BizCwmeetingScreeningTopicDTO> topicDTOList = bizCwmeetingScreeningDTO.getSubjectList();
        List<Map> meetAgendaWordList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(topicDTOList)) {
            for (int i = 0; i < topicDTOList.size(); i++) {
                MeetAgendaWordTable meetAgendaWordTable = new MeetAgendaWordTable();
                BizCwmeetingScreeningTopicDTO screeningTopicDTO = topicDTOList.get(i);
                meetAgendaWordTable.setNumber(i + 1);
                meetAgendaWordTable.setDate(screeningTopicDTO.getStartTime() + "-" + screeningTopicDTO.getEndTime());
                meetAgendaWordTable.setTitle(screeningTopicDTO.getSubjectName());
                meetAgendaWordTable.setReportPeopleName(screeningTopicDTO.getReportPeopleName());
                meetAgendaWordTable.setAttendLeader(screeningTopicDTO.getAttendLeader().stream().map(String::valueOf).collect(Collectors.joining("、")));
                meetAgendaWordTable.setAttendDeapartName(screeningTopicDTO.getAttendDeapartName().stream().map(String::valueOf).collect(Collectors.joining("、")));
                Map objectMap = BeanUtil.beanToMap(meetAgendaWordTable);
                meetAgendaWordList.add(objectMap);
            }
        }
        ExportData evaluation = WordUtil.createExportData("/cwmeetingAgenda.docx");
        MeetAgendaWord meetAgendaWord = new MeetAgendaWord();
        meetAgendaWord.setTitle(title);
        meetAgendaWord.setDate(agentDate == null ? " " : agentDate);

        evaluation.setData("meeting", BeanUtil.beanToMap(meetAgendaWord));
        evaluation.setTable("table", meetAgendaWordList);
        byte[] data = evaluation.getByteArr();
        try (OutputStream os = response.getOutputStream();) {
            FileNameUtil.setDownloadResponseHeader(response, title + ".docx");
            os.write(data);
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Data
    class MeetAgendaWord {
        String date;
        String title;

    }

    @Data
    class MeetAgendaWordTable {
        int number;
        String date;
        String title;
        String reportPeopleName;
        String attendLeader;
        String attendDeapartName;
    }
}
