package com.ctsi.committee.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.committee.entity.BizCwmeetingNotic;
import com.ctsi.committee.entity.BizCwmeetingScreening;
import com.ctsi.committee.entity.dto.BizCwmeetingScreeningDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingScreeningTopicDTO;
import com.ctsi.committee.mapper.BizCwmeetingNoticMapper;
import com.ctsi.committee.mapper.BizCwmeetingScreeningMapper;
import com.ctsi.committee.service.IBizCwmeetingScreeningService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 常委会议题筛选 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Slf4j
@Service
public class BizCwmeetingScreeningServiceImpl extends SysBaseServiceImpl<BizCwmeetingScreeningMapper, BizCwmeetingScreening> implements IBizCwmeetingScreeningService {

    @Autowired
    private BizCwmeetingScreeningMapper bizCwmeetingScreeningMapper;

    @Autowired
    private BizCwmeetingNoticMapper bizCwmeetingNoticMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizCwmeetingScreeningDTO> queryListPage(BizCwmeetingScreeningDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizCwmeetingScreening> queryWrapper = new LambdaQueryWrapper();

        IPage<BizCwmeetingScreening> pageData = bizCwmeetingScreeningMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        List<BizCwmeetingScreening> records = pageData.getRecords();
        // pojo 转换成 dto
        IPage<BizCwmeetingScreeningDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizCwmeetingScreeningDTO.class));

        // 由于 pojo 中 subjects 字段为字符串，dto 中为 List，因此需要将字符串转成对象集合。
        List<BizCwmeetingScreeningDTO> bizCwmeetingScreeningDTOS = data.getRecords();
        if (CollectionUtil.isNotEmpty(bizCwmeetingScreeningDTOS)) {
            bizCwmeetingScreeningDTOS.forEach(screeningDTO -> {
                records.forEach(screening ->{
                    if (screeningDTO.getId().equals(screening.getId())){
                        // 将Json字符串转换成对象集合
                        // getSubjects 返回的是字符串（对象集合序列化），需要反序列化为对象集合
                        screeningDTO.setSubjectList(JsonUtils.jsonToList(screening.getSubjects(), BizCwmeetingScreeningTopicDTO.class));
                    }
                });
            });
        }
        return new PageResult<BizCwmeetingScreeningDTO>(data.getRecords(),data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizCwmeetingScreeningDTO> queryList(BizCwmeetingScreeningDTO entityDTO) {
        LambdaQueryWrapper<BizCwmeetingScreening> queryWrapper = new LambdaQueryWrapper();
        List<BizCwmeetingScreening> listData = bizCwmeetingScreeningMapper.selectList(queryWrapper);
        List<BizCwmeetingScreeningDTO> BizCwmeetingScreeningDTOList = ListCopyUtil.copy(listData, BizCwmeetingScreeningDTO.class);
        return BizCwmeetingScreeningDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizCwmeetingScreeningDTO findOne(Long id) {
        BizCwmeetingScreening bizCwmeetingScreening = bizCwmeetingScreeningMapper.selectById(id);
        BizCwmeetingScreeningDTO bizCwmeetingScreeningDTO = BeanConvertUtils.copyProperties(bizCwmeetingScreening, BizCwmeetingScreeningDTO.class);
        String Subjects = bizCwmeetingScreening.getSubjects();
        if (StringUtils.isNotEmpty(Subjects)) {
            List<BizCwmeetingScreeningTopicDTO> bizCwmeetingScreeningTopicDTOS = JsonUtils.jsonToList(Subjects, BizCwmeetingScreeningTopicDTO.class);
            bizCwmeetingScreeningDTO.setSubjectList(bizCwmeetingScreeningTopicDTOS);
        }
        return bizCwmeetingScreeningDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizCwmeetingScreeningDTO create(BizCwmeetingScreeningDTO entityDTO) {
        BizCwmeetingScreening bizCwmeetingScreening = BeanConvertUtils.copyProperties(entityDTO, BizCwmeetingScreening.class);
        save(bizCwmeetingScreening);
        return BeanConvertUtils.copyProperties(bizCwmeetingScreening, BizCwmeetingScreeningDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizCwmeetingScreeningDTO entity) {
        // 如果议程已关联会议通知，则不允许编辑。
        LambdaQueryWrapper<BizCwmeetingNotic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizCwmeetingNotic::getMeetSerialNumber, entity.getMeetSerialNumber());
        Integer integer = bizCwmeetingNoticMapper.selectCount(queryWrapper);
        if (integer != 0) {
            throw new BusinessException("已关联会议通知，不能编辑");
        }

        // 如果议程未与会议通知关联，允许编辑，可再次编辑时间、可增删议题。
        BizCwmeetingScreening bizCwmeetingScreening1 = bizCwmeetingScreeningMapper.selectById(entity.getId());
        List<BizCwmeetingScreeningTopicDTO> subjectList = entity.getSubjectList();
        String subjectJson = JsonUtils.objectToJson(subjectList);
        bizCwmeetingScreening1.setSubjects(subjectJson);
        BizCwmeetingScreening bizCwmeetingScreening = BeanConvertUtils.copyProperties(entity, BizCwmeetingScreening.class);
        return bizCwmeetingScreeningMapper.updateById(bizCwmeetingScreening);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizCwmeetingScreeningMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizCwmeetingScreeningId
     * @return
     */
    @Override
    public boolean existByBizCwmeetingScreeningId(Long BizCwmeetingScreeningId) {
        if (BizCwmeetingScreeningId != null) {
            LambdaQueryWrapper<BizCwmeetingScreening> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizCwmeetingScreening::getId, BizCwmeetingScreeningId);
            List<BizCwmeetingScreening> result = bizCwmeetingScreeningMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizCwmeetingScreeningDTO> dataList) {
        List<BizCwmeetingScreening> result = ListCopyUtil.copy(dataList, BizCwmeetingScreening.class);
        return saveBatch(result);
    }


}
