package com.ctsi.committee.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.committee.entity.BizCwmeetingNotic;
import com.ctsi.committee.entity.dto.BizCwmeetingNoticDTO;
import com.ctsi.committee.mapper.BizCwmeetingNoticMapper;
import com.ctsi.committee.service.IBizCwmeetingNoticService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 常委会会议通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Slf4j
@Service
public class BizCwmeetingNoticServiceImpl extends SysBaseServiceImpl<BizCwmeetingNoticMapper, BizCwmeetingNotic> implements IBizCwmeetingNoticService {

    @Autowired
    private BizCwmeetingNoticMapper bizCwmeetingNoticMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizCwmeetingNoticDTO> queryListPage(BizCwmeetingNoticDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizCwmeetingNotic> queryWrapper = new LambdaQueryWrapper();

        IPage<BizCwmeetingNotic> pageData = bizCwmeetingNoticMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizCwmeetingNoticDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizCwmeetingNoticDTO.class));

        return new PageResult<BizCwmeetingNoticDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizCwmeetingNoticDTO> queryList(BizCwmeetingNoticDTO entityDTO) {
        LambdaQueryWrapper<BizCwmeetingNotic> queryWrapper = new LambdaQueryWrapper();
            List<BizCwmeetingNotic> listData = bizCwmeetingNoticMapper.selectList(queryWrapper);
            List<BizCwmeetingNoticDTO> BizCwmeetingNoticDTOList = ListCopyUtil.copy(listData, BizCwmeetingNoticDTO.class);
        return BizCwmeetingNoticDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizCwmeetingNoticDTO findOne(Long id) {
        BizCwmeetingNotic  bizCwmeetingNotic =  bizCwmeetingNoticMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizCwmeetingNotic,BizCwmeetingNoticDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizCwmeetingNoticDTO create(BizCwmeetingNoticDTO entityDTO) {
       BizCwmeetingNotic bizCwmeetingNotic =  BeanConvertUtils.copyProperties(entityDTO,BizCwmeetingNotic.class);
        save(bizCwmeetingNotic);
        return  BeanConvertUtils.copyProperties(bizCwmeetingNotic,BizCwmeetingNoticDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizCwmeetingNoticDTO entity) {
        BizCwmeetingNotic bizCwmeetingNotic = BeanConvertUtils.copyProperties(entity,BizCwmeetingNotic.class);
        return bizCwmeetingNoticMapper.updateById(bizCwmeetingNotic);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizCwmeetingNoticMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizCwmeetingNoticId
     * @return
     */
    @Override
    public boolean existByBizCwmeetingNoticId(Long BizCwmeetingNoticId) {
        if (BizCwmeetingNoticId != null) {
            LambdaQueryWrapper<BizCwmeetingNotic> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizCwmeetingNotic::getId, BizCwmeetingNoticId);
            List<BizCwmeetingNotic> result = bizCwmeetingNoticMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizCwmeetingNoticDTO> dataList) {
        List<BizCwmeetingNotic> result = ListCopyUtil.copy(dataList, BizCwmeetingNotic.class);
        return saveBatch(result);
    }


}
