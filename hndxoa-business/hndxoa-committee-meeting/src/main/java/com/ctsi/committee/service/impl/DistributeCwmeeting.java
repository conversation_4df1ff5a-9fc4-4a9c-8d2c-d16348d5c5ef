package com.ctsi.committee.service.impl;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctsi.committee.entity.BizCwmeetingInbox;
import com.ctsi.committee.service.IBizCwmeetingInboxService;
import com.ctsi.hndx.receive.entity.dto.DistributeCompanyPopulationDTO;
import com.ctsi.hndx.receive.entity.dto.UserListDTO;
import com.ctsi.hndx.receive.service.DistributeAbstract;
import com.ctsi.ssdc.security.SecurityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 常委会会议分发
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Component("distributeCwmeeting")
public class DistributeCwmeeting extends DistributeAbstract {

    @Resource(name = "bizCwmeetingInboxServiceImpl")
    private IBizCwmeetingInboxService bizCwmeetingInboxService;

    /**
     * 常委会会业务表
     */
    public static Map<String, String> cwmeetingTable = new HashMap() {{
        put("cwmeetingNotic", "会议通知");
        put("cwmeetingTopic", "会议议题");
        put("cwmeetingSummary", "会议纪要");
        put("cwmeetingMaterial", "会议材料");
        put("cwmeetingScreening", "议题筛选");
    }};

    /**
     * 判断是否为常委会分发
     * 是。分发收文到个人或外部单位收件箱.
     *
     * @param dcpData DistributeCompanyPopulationDTO
     * @return
     */
    @Override
    public Integer judge(DistributeCompanyPopulationDTO dcpData) {
        ArrayList<BizCwmeetingInbox> bizCwmeetingInboxList = new ArrayList<>();
        for (UserListDTO receiver : dcpData.getUserIdList()) {
            BizCwmeetingInbox entity = new BizCwmeetingInbox();
            entity.setTitle(dcpData.getTitle());
            entity.setTitleId(dcpData.getFormDataId());
            entity.setMeetingType(cwmeetingTable.get(dcpData.getSourceBusinessTable()));
            entity.setHasRead(0);
            entity.setSender(SecurityUtils.getCurrentRealName());
            entity.setReceiverId(receiver.getTemplateBusinessId());
            entity.setReceiverName(receiver.getTemplateBusinessName());
            entity.setReceiverDepartmentName(receiver.getUnitName());
            entity.setReceiverMobile(receiver.getMobile());
            entity.setSendDepartmentName(SecurityUtils.getCurrentCscpUserDetail().getDepartmentName());
            entity.setSendTime(LocalDateTimeUtil.now());
            bizCwmeetingInboxList.add(entity);
        }
        return bizCwmeetingInboxService.createDistributeCwmeeting(bizCwmeetingInboxList);
    }

    @Override
    public Integer distributeIndividual(DistributeCompanyPopulationDTO dcp) {
        return null;
    }

    @Override
    public Integer distributionUnit(DistributeCompanyPopulationDTO dcp) {
        return null;
    }
}
