package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 常委会议题筛选
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingScreeningDTO对象", description="常委会议题筛选")
public class BizCwmeetingScreeningDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 没有提交议题库
     */
    public final static Integer NO_SUBMIT = 0;

    /**
     * 提交议题库没有上会
     */
    public final static Integer SUBMIT_NO_MEET = 1;


    /**
     *  提交议题库并且上会
     */
    public final static Integer SUBMIT_AND_MEET = 2;


    /**
     * 选择议题
     */
    @ApiModelProperty(value = "议程标题")
    private String title;

    @ApiModelProperty(value = "会议日期")
    private LocalDate meetingDate;

    @ApiModelProperty(value = "选择议题集合")
    private List<BizCwmeetingScreeningTopicDTO> subjectList;
    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String companyName;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 是否会议通知，1表示选择，其他不选择
     */
    @ApiModelProperty(value = "是否会议通知，1表示选择，其他不选择")
    private Integer noticChoose;

    /**
     * 拟稿人
     */
    @ApiModelProperty(value = "拟稿人")
    private String createName;

    /**
     * 拟稿人电话
     */
    @ApiModelProperty(value = "拟稿人电话")
    private String mobile;

    /**
     * 申报日期（年月日）
     */
    @ApiModelProperty(value = "申报日期（年月日）")
    private LocalDate reportDate;

    /**
     * 会议序号（限4位数，0001-9999）
     */
    @ApiModelProperty(value = "会议序号（限4位数，0001-9999）")
    private String meetSerialNumber;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String dutyPeople;

    /**
     * 是否有正文
     */
    @ApiModelProperty(value = "是否有正文")
    private String document;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("流程实例id")
    private Long processInstanceId;
}
