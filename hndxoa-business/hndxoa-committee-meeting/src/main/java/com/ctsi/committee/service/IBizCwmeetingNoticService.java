package com.ctsi.committee.service;

import com.ctsi.committee.entity.dto.BizCwmeetingNoticDTO;
import com.ctsi.committee.entity.BizCwmeetingNotic;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 常委会会议通知 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
public interface IBizCwmeetingNoticService extends SysBaseServiceI<BizCwmeetingNotic> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizCwmeetingNoticDTO> queryListPage(BizCwmeetingNoticDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizCwmeetingNoticDTO> queryList(BizCwmeetingNoticDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizCwmeetingNoticDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizCwmeetingNoticDTO create(BizCwmeetingNoticDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizCwmeetingNoticDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizCwmeetingNoticId
     * @param code
     * @return
     */
    boolean existByBizCwmeetingNoticId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizCwmeetingNoticDTO> dataList);


}
