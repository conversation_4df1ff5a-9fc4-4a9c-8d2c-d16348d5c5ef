package com.ctsi.committee.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 议程关联的议题库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "BizCwmeetingScreeningTopicDTO对象", description = "议程关联的议题库")
public class BizCwmeetingScreeningTopicDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会议议题id，没有为空")
    private Long id;

    @ApiModelProperty("会议议题标题内容")
    private String subjectName;

    @ApiModelProperty("会议议题的排序号")
    private Integer orderBy;


    @ApiModelProperty("会议议题汇报开始时间")
    private String startTime;

    @ApiModelProperty("会议议题汇报结束时间")
    private String endTime;

    /**
     * 拟列席市领导
     */
    @ApiModelProperty(value = "拟列席市领导")
    private List<String> attendLeader;

    /**
     * 拟列席部门
     */
    @ApiModelProperty(value = "拟列席部门")
    private List<String> attendDeapartName;

    /**
     * 汇报人员的姓名，多个逗号隔开
     */
    @ApiModelProperty(value = "汇报人员的姓名")
    private String reportPeopleName;
}
