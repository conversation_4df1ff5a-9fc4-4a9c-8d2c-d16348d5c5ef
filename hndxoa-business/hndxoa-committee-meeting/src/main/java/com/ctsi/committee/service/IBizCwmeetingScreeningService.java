package com.ctsi.committee.service;

import com.ctsi.committee.entity.dto.BizCwmeetingScreeningDTO;
import com.ctsi.committee.entity.BizCwmeetingScreening;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 常委会议题筛选 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface IBizCwmeetingScreeningService extends SysBaseServiceI<BizCwmeetingScreening> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizCwmeetingScreeningDTO> queryListPage(BizCwmeetingScreeningDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizCwmeetingScreeningDTO> queryList(BizCwmeetingScreeningDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizCwmeetingScreeningDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizCwmeetingScreeningDTO create(BizCwmeetingScreeningDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizCwmeetingScreeningDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizCwmeetingScreeningId
     * @param code
     * @return
     */
    boolean existByBizCwmeetingScreeningId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizCwmeetingScreeningDTO> dataList);


}
