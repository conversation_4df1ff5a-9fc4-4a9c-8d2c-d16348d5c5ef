package com.ctsi.committee.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 常委会会议收件箱入参对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizCwmeetingInboxParamDTO对象", description="常委会会议收件箱入参对象")
public class BizCwmeetingInboxParamDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 是否查阅
     */
    @ApiModelProperty(value = "是否查阅，0表示未查阅，1表示已查阅")
    private Integer hasRead;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;


    /**
     * 收件人ID
     */
    @ApiModelProperty(value = "收件人ID")
    private Long receiverId;
    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    private String sendDepartmentName;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

}
