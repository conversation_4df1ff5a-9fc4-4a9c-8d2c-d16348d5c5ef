package com.ctsi.committee.controller;

import com.ctsi.committee.entity.dto.BizCwmeetingInboxParamDTO;
import com.ctsi.committee.entity.dto.BizCwmeetingInboxResultDTO;
import com.ctsi.committee.service.IBizCwmeetingInboxService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizCwmeetingInbox")
@Api(value = "常委会会议收件箱", tags = "常委会会议收件箱接口")
public class BizCwmeetingInboxController extends BaseController {

    private static final String ENTITY_NAME = "bizCwmeetingInbox";

    @Autowired
    private IBizCwmeetingInboxService bizCwmeetingInboxService;


    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizCwmeetingInboxPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizCwmeetingInboxResultDTO>> queryBizCwmeetingNoticPage(BizCwmeetingInboxParamDTO bizCwmeetingInboxParamDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizCwmeetingInboxService.queryListPage(bizCwmeetingInboxParamDTO, basePageForm));
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新常委会会议收件箱公文查阅状态(权限code码为：cscp.bizCwmeetingInbox.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新常委会会议收件箱公文查阅状态")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCwmeetingInbox.update')")
    public ResultVO update(@ApiParam(required = true, value = "公文id和公文类型meetType") @RequestBody HashMap<String, String> map) {
        int count = bizCwmeetingInboxService.update(Long.valueOf(map.get("id")), map.get("meetType"));
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }
}
