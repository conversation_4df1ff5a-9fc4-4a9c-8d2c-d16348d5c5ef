package com.ctsi.personMatters.entity.dto;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlRootElement(name = "JiaTingChengYuan")
@XmlAccessorType(XmlAccessType.FIELD)
public class Item {
    @XmlElement(name = "Item")
    private List<TPersonDossierFamilyDTO> tPersonDossierFamilyDTOs;
}
