package com.ctsi.regulations.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.regulations.entity.dto.TRegulationsConfirmSaveDTO;
import com.ctsi.regulations.service.ITRegulationsConfirmSaveService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsConfirmSave")
@Api(value = "法规室整改确认驳回和存档", tags = "法规室整改确认驳回和存档接口")
public class TRegulationsConfirmSaveController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsConfirmSave";

    @Autowired
    private ITRegulationsConfirmSaveService tRegulationsConfirmSaveService;



    /**
     *  新增法规室整改确认驳回和存档批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsConfirmSave.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室整改确认驳回和存档批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsConfirmSave.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsConfirmSaveDTO> tRegulationsConfirmSaveList) {
       Boolean  result = tRegulationsConfirmSaveService.insertBatch(tRegulationsConfirmSaveList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsConfirmSave.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室整改确认驳回和存档数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsConfirmSave.add')")
    public ResultVO<TRegulationsConfirmSaveDTO> create(@RequestBody TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO)  {
        TRegulationsConfirmSaveDTO result = tRegulationsConfirmSaveService.create(tRegulationsConfirmSaveDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsConfirmSave.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新法规室整改确认驳回和存档数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsConfirmSave.update')")
    public ResultVO update(@RequestBody TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO) {
	    Assert.notNull(tRegulationsConfirmSaveDTO.getId(), "general.IdNotNull");
        int count = tRegulationsConfirmSaveService.update(tRegulationsConfirmSaveDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室整改确认驳回和存档数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsConfirmSave.delete)", notes = "传入参数")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsConfirmSave.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsConfirmSaveService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO = tRegulationsConfirmSaveService.findOne(id);
        return ResultVO.success(tRegulationsConfirmSaveDTO);
    }

    /**
    *  分页查询多条数据已存档.
    */
    @PostMapping("/queryTRegulationsConfirmSavePageArchive")
    @ApiOperation(value = "翻页查询多条数据（已存档）", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsConfirmSaveDTO>> queryTRegulationsConfirmSavePageArchive(@RequestBody TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsConfirmSaveService.queryListPageArchive(tRegulationsConfirmSaveDTO, basePageForm));
    }

    /**
     *  分页查询多条数据.
     */
    @PostMapping("/queryTRegulationsConfirmSavePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsConfirmSaveDTO>> queryTRegulationsConfirmSavePage(@RequestBody TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsConfirmSaveService.queryListPage(tRegulationsConfirmSaveDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTRegulationsConfirmSave")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsConfirmSaveDTO>> queryTRegulationsConfirmSave(@RequestBody TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO) {
       List<TRegulationsConfirmSaveDTO> list = tRegulationsConfirmSaveService.queryList(tRegulationsConfirmSaveDTO);
       return ResultVO.success(new ResResult<TRegulationsConfirmSaveDTO>(list));
   }


    /**
     * 更新为已读
     */
    @GetMapping("/updateIsRead")
    @ApiOperation(value = "更新为已读", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> updateIsRead(Long id) {
        Boolean count = tRegulationsConfirmSaveService.updateIsRead(id);
        return ResultVO.success(count);
    }

}
