package com.ctsi.regulations.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.regulations.entity.TRegulationsConfirmSave;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsHandle;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.regulations.mapper.TRegulationsConfirmSaveMapper;
import com.ctsi.regulations.mapper.TRegulationsHandleMapper;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.regulations.service.ITRegulationsExaminationService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.BizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsExamination")
@Api(value = "法规室审查表", tags = "法规室审查表接口")
public class TRegulationsExaminationController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsExamination";

    @Autowired
    private ITRegulationsExaminationService tRegulationsExaminationService;

    @Autowired
    private BizService bizService;

    @Autowired
    ITRegulationsExaminationProcessService regulationsExaminationProcessService;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    TRegulationsConfirmSaveMapper regulationsConfirmSaveMapper;

    @Autowired
    TRegulationsHandleMapper regulationsHandleMapper;


    /**
     *  新增法规室审查表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsExamination.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查表批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExamination.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsExaminationDTO> tRegulationsExaminationList) {
       Boolean  result = tRegulationsExaminationService.insertBatch(tRegulationsExaminationList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsExamination.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExamination.add')")
    public ResultVO<TRegulationsExaminationDTO> create(@RequestBody TRegulationsExaminationDTO tRegulationsExaminationDTO)  {
        TRegulationsExaminationDTO result = tRegulationsExaminationService.create(tRegulationsExaminationDTO);
        return ResultVO.success(result);
    }

    /**
     *  审查.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsExamination.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "审查")
//    // @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExamination.update')")
    public ResultVO update(@RequestBody TRegulationsExaminationDTO tRegulationsExaminationDTO) {
	    Assert.notNull(tRegulationsExaminationDTO.getId(), "general.IdNotNull");
        int count = tRegulationsExaminationService.update(tRegulationsExaminationDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

//    批量审查
    /**
     *  审查.
     */
    @PostMapping("/updateList")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsExamination.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "批量审查")
//    // @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExamination.update')")
    public ResultVO updateList(@RequestBody List<TRegulationsExaminationDTO> examinationDTOList) {
        int count=0;
        for (TRegulationsExaminationDTO tRegulationsExaminationDTO : examinationDTOList) {
            Assert.notNull(tRegulationsExaminationDTO.getId(), "general.IdNotNull");
            count =count+ tRegulationsExaminationService.update(tRegulationsExaminationDTO);
        }
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/updateBatch")
    @ApiOperation(value = "批量更新(权限code码为：cscp.tRegulationsHandle.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "批量更新")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.update')")
    public ResultVO updateBatch(@RequestBody List<TRegulationsExaminationDTO> examinationDTOList) {
        int count = 0;
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        for (TRegulationsExaminationDTO regulationsExaminationDTO : examinationDTOList) {
            TRegulationsExamination byId = tRegulationsExaminationService.getById(regulationsExaminationDTO.getId());
            //选择直接通过只生效一次
            if (regulationsExaminationDTO.getReviewOpinions() == 1) {
                //在TRegulationsConfirmSave 存档创建一条数据库
                //先查询存档表是否已存在
                LambdaQueryWrapper<TRegulationsConfirmSave> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(TRegulationsConfirmSave::getExaminationId, byId.getId());
                Integer i = regulationsConfirmSaveMapper.selectCountNoAdd(lambdaQueryWrapper);
                if (i == 0) {
                    TRegulationsConfirmSave regulationsConfirmSave = new TRegulationsConfirmSave();
                    regulationsConfirmSave.setIsArchive(1);
                    regulationsConfirmSave.setExaminationId(byId.getId());
                    regulationsConfirmSave.setFileName(byId.getFileName());
                    regulationsConfirmSave.setFileNumber(byId.getFileNumber());
                    regulationsConfirmSave.setReviewOpinions(1);
                    regulationsConfirmSave.setReportingUnitName(byId.getReportingUnitName());
                    regulationsConfirmSave.setReportingTime(byId.getReportingTime());
                    regulationsConfirmSave.setArchiveTime(LocalDateTime.now());
                    regulationsConfirmSaveMapper.insert(regulationsConfirmSave);
                }
//            tRegulationsExamination.setSkipStatus("E,F,G,H,I");
                byId.setReviewOpinions(regulationsExaminationDTO.getReviewOpinions());
                //這裡可以确认不需要整改反馈
                if(!StringUtils.isEmpty(byId.getSkipStatus())) {
                    List<String> list = Arrays.asList(byId.getSkipStatus().split(","));
                    Set<String> skipSet = new HashSet<>(list);
                    skipSet.add("I");
                    String skipString = String.join(",", skipSet);
                    byId.setSkipStatus(skipString);
                }else{
                    regulationsExaminationDTO.setSkipStatus("I");
                }
                regulationsExaminationDTO.setCurrentStage("J");
                regulationsExaminationProcessService.updateStatus(byId, "J", 3, companyName);
                regulationsExaminationProcessService.updateStatusAllEnd(byId,"J",3,null);
            }
            count = count + tRegulationsExaminationService.updateNoProcess(regulationsExaminationDTO);
        }
        //更新至允许提交一次
        if(!examinationDTOList.isEmpty()) {
            LambdaQueryWrapper<TRegulationsHandle> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(TRegulationsHandle::getExaminationId, examinationDTOList.get(0).getId());
            TRegulationsHandle tRegulationsHandle = regulationsHandleMapper.selectOneNoAdd(lambdaQueryWrapper);
            tRegulationsHandle.setIsOfficialDocuments(1);
            regulationsHandleMapper.updateById(tRegulationsHandle);
        }
        for (TRegulationsExaminationDTO regulationsExaminationDTO : examinationDTOList) {
            TRegulationsExamination tRegulationsExamination = BeanConvertUtils.copyProperties(regulationsExaminationDTO, TRegulationsExamination.class);
            if (!tRegulationsExamination.getReviewOpinions().toString().equals("1") ) {
                regulationsExaminationProcessService.updateStatus(tRegulationsExamination, "G", 3, companyName);
            }
        }
        List<Long> ids = examinationDTOList.stream().map(i -> i.getId()).collect(Collectors.toList());
        bizService.pushCheckResultFgs(ids);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室审查表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsExamination.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExamination.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsExaminationService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsExaminationDTO tRegulationsExaminationDTO = tRegulationsExaminationService.findOne(id);
        return ResultVO.success(tRegulationsExaminationDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @PostMapping("/queryTRegulationsExaminationPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsExaminationDTO>> queryTRegulationsExaminationPage(@RequestBody TRegulationsExaminationDTO tRegulationsExaminationDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsExaminationService.queryListPage(tRegulationsExaminationDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTRegulationsExamination")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsExaminationDTO>> queryTRegulationsExamination(@RequestBody TRegulationsExaminationDTO tRegulationsExaminationDTO) {
       List<TRegulationsExaminationDTO> list = tRegulationsExaminationService.queryList(tRegulationsExaminationDTO);
       return ResultVO.success(new ResResult<TRegulationsExaminationDTO>(list));
   }


    /**
     * 查询多条数据.不分页
     */
    @PostMapping("/queryTRegulationsExaminationByIdList")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<TRegulationsExaminationDTO>> queryTRegulationsExaminationByIdList(@RequestBody List<Long> idList) {
        List<TRegulationsExaminationDTO> list = tRegulationsExaminationService.queryListByIdList(idList);
        return ResultVO.success(new ResResult<TRegulationsExaminationDTO>(list));
    }





    @PostMapping("/downloadTemplate")
    @ApiOperation(value = "下载模板", notes = "传入参数")
    public void downloadTemplate(HttpServletResponse response, Integer type,@RequestBody TRegulationsExaminationDTO tRegulationsExaminationDTO ) throws IOException {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        String nowDate = currentDate.format(formatter);
        String username = SecurityUtils.getCurrentCscpUserDetail().getRealName();
        String mobile = SecurityUtils.getCurrentCscpUserDetail().getMobile();
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        if(StringUtils.isEmpty(companyName)){
            companyName="";
        }
        if(type==1) {
            // 填充内容
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("{{时间}}", nowDate);
            placeholders.put("{{打电话人}}", username+" "+mobile);
            placeholders.put("{{接电话人}}", tRegulationsExaminationDTO.getContactsName()+tRegulationsExaminationDTO.getTelephone());
            String suggestion=tRegulationsExaminationDTO.getFileName()+"("+tRegulationsExaminationDTO.getFileNumber()+")";
            placeholders.put("{{建议内容}}", suggestion);
            placeholders.put("{{日期}}", nowDate);
            placeholders.put("{{单位}}", companyName);
            tRegulationsExaminationService.downloadTemplate(response, null, placeholders, "/templates/jyjld.docx", "《"+tRegulationsExaminationDTO.getFileNumber()+"建议记录单》");
        }else if(type==2){
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("{{时间}}", nowDate);
            placeholders.put("{{打电话人}}", username+" "+mobile);
            placeholders.put("{{接电话人}}", tRegulationsExaminationDTO.getContactsName()+tRegulationsExaminationDTO.getTelephone());
            String suggestion=tRegulationsExaminationDTO.getFileName();
            placeholders.put("{{告知内容}}", suggestion+"("+tRegulationsExaminationDTO.getFileNumber()+")");
            placeholders.put("{{日期}}", nowDate);
            placeholders.put("{{单位}}", companyName);
            tRegulationsExaminationService.downloadTemplate(response, null, placeholders, "/templates/gzjld.docx", "《"+tRegulationsExaminationDTO.getFileNumber()+"告知记录单》");
        } else if (type==3) {
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("{{年份}}", String.valueOf(currentDate.getYear()));
            placeholders.put("{{来文单位}}", tRegulationsExaminationDTO.getReportingUnitName());
            String suggestion=tRegulationsExaminationDTO.getFileName()+"("+tRegulationsExaminationDTO.getFileNumber()+")";
            placeholders.put("{{内容}}", suggestion);
            placeholders.put("{{日期}}", nowDate);
            placeholders.put("{{单位}}", companyName);
            tRegulationsExaminationService.downloadTemplate(response, null, placeholders, "/templates/txh.docx", "《"+tRegulationsExaminationDTO.getFileNumber()+"提醒函》");
        } else if (type==4) {
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("{{年份}}", String.valueOf(currentDate.getYear()));
            placeholders.put("{{来文单位}}", tRegulationsExaminationDTO.getReportingUnitName());
            String suggestion=tRegulationsExaminationDTO.getFileName()+"("+tRegulationsExaminationDTO.getFileNumber()+")";
            placeholders.put("{{内容}}", suggestion);
            placeholders.put("{{日期}}", nowDate);
            placeholders.put("{{单位}}", companyName);
            tRegulationsExaminationService.downloadTemplate(response, null, placeholders, "/templates/jzh.docx", "《"+tRegulationsExaminationDTO.getFileNumber()+"纠正函》");
        }
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryProcessStatus")
    @ApiOperation(value = "查询呈批单当前状态", notes = "传入参数")
    //// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO queryProcessStatus(String cpbId) {
        TRegulationsExaminationDTO regulationsExaminationDTO=new TRegulationsExaminationDTO();
        LambdaQueryWrapper<CscpProcBase> cscpProcBaseLambdaQueryWrapper=new LambdaQueryWrapper<>();
        cscpProcBaseLambdaQueryWrapper.eq(CscpProcBase::getFormDataId,cpbId);
        CscpProcBase cscpProcBase = cscpProcBaseRepository.selectOneNoAdd(cscpProcBaseLambdaQueryWrapper);
        if(cscpProcBase.getBpmStatus()==3){
            regulationsExaminationDTO.setCpbBpmStatus("3");
            return ResultVO.success(regulationsExaminationDTO);
        }else{
            regulationsExaminationDTO.setCpbBpmStatus(String.valueOf(cscpProcBase.getBpmStatus()));
        }
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,cscpProcBase.getProcInstId());
        ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
        regulationsExaminationDTO.setCpbCurrentNode(processAssignee.getNodeName());
        regulationsExaminationDTO.setCpbCurrentUser(processAssignee.getAssigneeName());
        return ResultVO.success(regulationsExaminationDTO);
    }

}
