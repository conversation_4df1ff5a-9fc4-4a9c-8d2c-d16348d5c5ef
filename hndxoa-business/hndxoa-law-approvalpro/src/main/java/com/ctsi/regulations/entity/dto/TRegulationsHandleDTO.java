package com.ctsi.regulations.entity.dto;

import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 法规室审查处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TRegulationsHandleDTO对象", description="法规室审查处理")
public class TRegulationsHandleDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private String examinationId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 报备单位ID
     */
    @ApiModelProperty(value = "报备单位ID")
    private String reportCompanyId;

    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportCompanyName;

    /**
     * 送审日期
     */
    @ApiModelProperty(value = "送审日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportingTime;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;


    /**
     * 正文文件ID
     */
    @ApiModelProperty(value = "正文文件ID")
    private Long mainTestFileId;

    /**
     * 正文文件名
     */
    @ApiModelProperty(value = "正文文件名")
    private String mainTestFileName;

    /**
     * 正文文件路径
     */
    @ApiModelProperty(value = "正文文件路径")
    private String mainTestFilePath;

    /**
     * 文件目录文件ID
     */
    @ApiModelProperty(value = "文件目录文件ID")
    private Long fileDirectoryFileId;

    /**
     * 文件目录文件名
     */
    @ApiModelProperty(value = "文件目录文件名")
    private String fileDirectoryFileName;

    /**
     * 文件目录文件路径
     */
    @ApiModelProperty(value = "文件目录文件路径")
    private String fileDirectoryFilePath;

    /**
     * 附件文件ID
     */
    @ApiModelProperty(value = "附件文件ID")
    private Long annexFileId;

    /**
     * 附件文件名
     */
    @ApiModelProperty(value = "附件文件名")
    private String annexFileName;

    /**
     * 附件文件路径
     */
    @ApiModelProperty(value = "附件文件路径")
    private String annexFilePath;


    /**
     * 流程当前环节
     */
    @ApiModelProperty(value = "流程当前环节")
    private String currentStage;


    /**
     * 流程当前状态
     */
    @ApiModelProperty(value = "流程当前状态，0未完成，1已完成")
    private Integer currentStatus;


    /**
     * 流程当前用户
     */
    @ApiModelProperty(value = "流程当前用户")
    private String currentUserName;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;

    /**
     * 标题及其内容
     */
    @ApiModelProperty(value = "标题及其内容")
    private String title;

    /**
     * 正文
     */
    @ApiModelProperty(value = "正文")
    private String document;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;


    /**
     * 0未提交，1已提交确认
     */
    @ApiModelProperty(value = "0未提交，1已提交确认")
    private Integer isOfficialDocuments;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处室负责人")
    private String departmentHeadName;

    @ApiModelProperty("表单流程信息")
    private TaskVO taskVO;

    @ApiModelProperty(value = "领导意见")
    private String opinion;

    private LocalDateTime createTime;

    @ApiModelProperty(value = "文件密级:数据字典中文值")
    private String securityClassified;
    @ApiModelProperty(value = "文件密级:数据字CODE")
    private Integer securityClassifiedCode;

    /**
     * 定密依据
     */
    @ApiModelProperty(value = "定密依据")
    private String classificationBasis;


    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;
}
