package com.ctsi.regulations.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationProcessDTO;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsExaminationProcess")
@Api(value = "法规室审查表流程记录表", tags = "法规室审查表流程记录表接口")
public class TRegulationsExaminationProcessController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsExaminationProcess";

    @Autowired
    private ITRegulationsExaminationProcessService tRegulationsExaminationProcessService;



    /**
     *  新增法规室审查表流程记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsExaminationProcess.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查表流程记录表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExaminationProcess.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsExaminationProcessDTO> tRegulationsExaminationProcessList) {
       Boolean  result = tRegulationsExaminationProcessService.insertBatch(tRegulationsExaminationProcessList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsExaminationProcess.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查表流程记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExaminationProcess.add')")
    public ResultVO<TRegulationsExaminationProcessDTO> create(@RequestBody TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO)  {
        TRegulationsExaminationProcessDTO result = tRegulationsExaminationProcessService.create(tRegulationsExaminationProcessDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsExaminationProcess.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新法规室审查表流程记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExaminationProcess.update')")
    public ResultVO update(@RequestBody TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO) {
	    Assert.notNull(tRegulationsExaminationProcessDTO.getId(), "general.IdNotNull");
        int count = tRegulationsExaminationProcessService.update(tRegulationsExaminationProcessDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室审查表流程记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsExaminationProcess.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsExaminationProcess.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsExaminationProcessService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO = tRegulationsExaminationProcessService.findOne(id);
        return ResultVO.success(tRegulationsExaminationProcessDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @PostMapping("/queryTRegulationsExaminationProcessPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsExaminationProcessDTO>> queryTRegulationsExaminationProcessPage( @RequestBody TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsExaminationProcessService.queryListPage(tRegulationsExaminationProcessDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @PostMapping("/queryTRegulationsExaminationProcess")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsExaminationProcessDTO>> queryTRegulationsExaminationProcess(@RequestBody  TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO) {
       List<TRegulationsExaminationProcessDTO> list = tRegulationsExaminationProcessService.queryList(tRegulationsExaminationProcessDTO);
       return ResultVO.success(new ResResult<TRegulationsExaminationProcessDTO>(list));
   }


    @GetMapping("/initProcess")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public void initProcess(TRegulationsExaminationProcess tRegulationsExaminationProcess) {
        TRegulationsExamination regulationsExamination =new TRegulationsExamination();
        regulationsExamination.setId(1L);
        tRegulationsExaminationProcessService.initProcess(regulationsExamination );
    }

}
