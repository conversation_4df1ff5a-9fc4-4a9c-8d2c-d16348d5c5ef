package com.ctsi.regulations.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.regulations.entity.dto.TRegulationsProblemDescriptionDTO;
import com.ctsi.regulations.service.ITRegulationsProblemDescriptionService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsProblemDescription")
@Api(value = "法规室审查问题说明表", tags = "法规室审查问题说明表接口")
public class TRegulationsProblemDescriptionController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsProblemDescription";

    @Autowired
    private ITRegulationsProblemDescriptionService tRegulationsProblemDescriptionService;



    /**
     *  新增问题说明表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsProblemDescription.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增问题说明表批量数据")
   //  @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsProblemDescription.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsProblemDescriptionDTO> tRegulationsProblemDescriptionList) {
       Boolean  result = tRegulationsProblemDescriptionService.insertBatch(tRegulationsProblemDescriptionList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsProblemDescription.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增问题说明表数据")
//   //  @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsProblemDescription.add')")
    public ResultVO<TRegulationsProblemDescriptionDTO> create(@RequestBody TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO)  {
        TRegulationsProblemDescriptionDTO result = tRegulationsProblemDescriptionService.create(tRegulationsProblemDescriptionDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsProblemDescription.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新问题说明表数据")
//   //  @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsProblemDescription.update')")
    public ResultVO update(@RequestBody TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO) {
	    Assert.notNull(tRegulationsProblemDescriptionDTO.getId(), "general.IdNotNull");
        int count = tRegulationsProblemDescriptionService.update(tRegulationsProblemDescriptionDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除问题说明表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsProblemDescription.delete)", notes = "传入参数")
   //  @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsProblemDescription.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsProblemDescriptionService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO = tRegulationsProblemDescriptionService.findOne(id);
        return ResultVO.success(tRegulationsProblemDescriptionDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @PostMapping("/queryTRegulationsProblemDescriptionPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsProblemDescriptionDTO>> queryTRegulationsProblemDescriptionPage(@RequestBody TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsProblemDescriptionService.queryListPage(tRegulationsProblemDescriptionDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @PostMapping("/queryTRegulationsProblemDescription")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsProblemDescriptionDTO>> queryTRegulationsProblemDescription(@RequestBody TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO) {
       List<TRegulationsProblemDescriptionDTO> list = tRegulationsProblemDescriptionService.queryList(tRegulationsProblemDescriptionDTO);
       return ResultVO.success(new ResResult<TRegulationsProblemDescriptionDTO>(list));
   }


    /**
     * 更新为已读
     */
    @GetMapping("/updateIsRead")
    @ApiOperation(value = "更新为已读", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Integer> updateIsRead(Long examinationId) {
        Integer count = tRegulationsProblemDescriptionService.updateIsRead(examinationId);
        return ResultVO.success(count);
    }
}
