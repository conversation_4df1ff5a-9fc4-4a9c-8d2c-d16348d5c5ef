package com.ctsi.approvalpro.controller;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.entity.dto.TLawFileMakeDiscussionApproveDTO;
import com.ctsi.approvalpro.service.ITLawFileMakeDiscussionApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawFileMakeDiscussionApprove")
@Api(value = "文件制定审议批准表", tags = "文件制定审议批准表接口")
public class TLawFileMakeDiscussionApproveController extends BaseController {

    private static final String ENTITY_NAME = "tLawFileMakeDiscussionApprove";

    @Autowired
    private ITLawFileMakeDiscussionApproveService tLawFileMakeDiscussionApproveService;



    /**
     *  新增文件制定审议批准表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawFileMakeDiscussionApprove.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增文件制定审议批准表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileMakeDiscussionApprove.add')")
    public ResultVO createBatch(@RequestBody List<TLawFileMakeDiscussionApproveDTO> tLawFileMakeDiscussionApproveList) {
       Boolean  result = tLawFileMakeDiscussionApproveService.insertBatch(tLawFileMakeDiscussionApproveList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tLawFileMakeDiscussionApprove.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增文件制定审议批准表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawFileMakeDiscussionApprove.add')")
    public ResultVO<TLawFileMakeDiscussionApproveDTO> create(@RequestBody TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO)  {
        TLawFileMakeDiscussionApproveDTO result = tLawFileMakeDiscussionApproveService.create(tLawFileMakeDiscussionApproveDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tLawFileMakeDiscussionApprove.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新文件制定审议批准表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileMakeDiscussionApprove.update')")
    public ResultVO update(@RequestBody TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
	    Assert.notNull(tLawFileMakeDiscussionApproveDTO.getId(), "general.IdNotNull");
        int count = tLawFileMakeDiscussionApproveService.update(tLawFileMakeDiscussionApproveDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除文件制定审议批准表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tLawFileMakeDiscussionApprove.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileMakeDiscussionApprove.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLawFileMakeDiscussionApproveService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        LawFileMakeDiscussApproveDetail approveDetail = tLawFileMakeDiscussionApproveService.getDetail(id);
        return ResultVO.success(approveDetail);
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/getByApprovalId/{approvalId}")
    @ApiOperation(value = "approvalId查询数据", notes = "传入参数")
    public ResultVO getByApprovalId(@PathVariable Long approvalId) {
        LawFileMakeDiscussApproveDetail approveDetail = tLawFileMakeDiscussionApproveService.getByApprovalId(approvalId);
        return ResultVO.success(approveDetail);
    }


    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLawFileMakeDiscussionApprovePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawFileMakeDiscussionApproveDTO>> queryTLawFileMakeDiscussionApprovePage(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLawFileMakeDiscussionApproveService.queryListPage(tLawFileMakeDiscussionApproveDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLawFileMakeDiscussionApprove")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TLawFileMakeDiscussionApproveDTO>> queryTLawFileMakeDiscussionApprove(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
       List<TLawFileMakeDiscussionApproveDTO> list = tLawFileMakeDiscussionApproveService.queryList(tLawFileMakeDiscussionApproveDTO);
       return ResultVO.success(new ResResult<TLawFileMakeDiscussionApproveDTO>(list));
   }


    /**
     *
     * @param tLawFileMakeDiscussionApproveDTO
     * @return
     */
    @PostMapping("/upload")
    @ApiOperation(value = "上传", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "上传")
    public ResultVO upload(@RequestBody TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
        tLawFileMakeDiscussionApproveService.upload(tLawFileMakeDiscussionApproveDTO);
        return ResultVO.success();
    }

    /**
     *
     * @param tLawFileMakeDiscussionApproveDTO
     * @return
     */
    @PostMapping("/skip")
    @ApiOperation(value = "跳过", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过")
    public ResultVO skip(@RequestBody TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
        tLawFileMakeDiscussionApproveService.skip(tLawFileMakeDiscussionApproveDTO);
        return ResultVO.success();
    }
}
