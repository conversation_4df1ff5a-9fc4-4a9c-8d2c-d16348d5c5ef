

CREATE TABLE "t_law_approvalpro"
(
    "id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(32),
    "create_time" TIMESTAMP(0),
    "declare_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "department_name" VARCHAR(32),
    "company_id" BIGINT,
    "company_name" VARCHAR(32),
    "tenant_id" BIGINT,
    "deleted" INT,
    "approval_file_name" VARCHAR(512),
    "contact_person" VARCHAR(64),
    "contact_phone" VARCHAR(64),
    "file_evidence" CLOB,
    "file_modality_type" VARCHAR(10),
    "file_modality_value" VARCHAR(64),
    "examine_department_id" BIGINT,
    "examine_department_name" VARCHAR(32),
    "examine_company_id" BIGINT,
    "examine_company_name" VARCHAR(32),
    "examine_id" BIGINT,
    "examine_name" VARCHAR(32),
    "examine_contact_person" VARCHAR(64),
    "examine_contact_phone" VARCHAR(64),
    "status" CHAR(1),
    "advise_and_reason" CLOB,
    "examine_phone" CHARACTER(64),
    "file_type_first" VARCHAR(10),
    "file_type_sec" VARCHAR(10),
    "file_type_third" VARCHAR(10),
    "file_type_first_value" VARCHAR(64),
    "file_type_sec_value" VARCHAR(64),
    "file_type_third_value" VARCHAR(64),
    "file_modality_type_sec" VARCHAR(15),
    "file_modality_type_sec_value" VARCHAR(64),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "SWOA", CLUSTERBTR) ;
COMMENT ON TABLE "t_law_approvalpro" IS '法规室集中立项';
COMMENT ON COLUMN "t_law_approvalpro"."advise_and_reason" IS '是否纳入计划的建议及理由';
COMMENT ON COLUMN "t_law_approvalpro"."approval_file_name" IS '申报的文件名';
COMMENT ON COLUMN "t_law_approvalpro"."company_id" IS '单位ID';
COMMENT ON COLUMN "t_law_approvalpro"."company_name" IS '单位名称';
COMMENT ON COLUMN "t_law_approvalpro"."contact_person" IS '联系人';
COMMENT ON COLUMN "t_law_approvalpro"."contact_phone" IS '联系方式';
COMMENT ON COLUMN "t_law_approvalpro"."create_by" IS '起草人id';
COMMENT ON COLUMN "t_law_approvalpro"."create_name" IS '起草人名称';
COMMENT ON COLUMN "t_law_approvalpro"."create_time" IS '起草时间';
COMMENT ON COLUMN "t_law_approvalpro"."declare_time" IS '申报时间';
COMMENT ON COLUMN "t_law_approvalpro"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "t_law_approvalpro"."department_id" IS '起草部门ID';
COMMENT ON COLUMN "t_law_approvalpro"."department_name" IS '起草部门名称';
COMMENT ON COLUMN "t_law_approvalpro"."examine_company_id" IS '审核人单位id';
COMMENT ON COLUMN "t_law_approvalpro"."examine_company_name" IS '审核人单位名称';
COMMENT ON COLUMN "t_law_approvalpro"."examine_contact_person" IS '预留  如果需要修改';
COMMENT ON COLUMN "t_law_approvalpro"."examine_contact_phone" IS '预留  如果需要修改';
COMMENT ON COLUMN "t_law_approvalpro"."examine_department_id" IS '审核人部门';
COMMENT ON COLUMN "t_law_approvalpro"."examine_department_name" IS '审核人部门名称';
COMMENT ON COLUMN "t_law_approvalpro"."examine_id" IS '审核联系人id';
COMMENT ON COLUMN "t_law_approvalpro"."examine_name" IS '审核联系人';
COMMENT ON COLUMN "t_law_approvalpro"."examine_phone" IS '审核人联系电话';
COMMENT ON COLUMN "t_law_approvalpro"."file_evidence" IS '申报的发文依据';
COMMENT ON COLUMN "t_law_approvalpro"."file_modality_type" IS '申报的发文形式code';
COMMENT ON COLUMN "t_law_approvalpro"."file_modality_value" IS '申报的发文形式value';
COMMENT ON COLUMN "t_law_approvalpro"."id" IS '主键ID';
COMMENT ON COLUMN "t_law_approvalpro"."status" IS '审核状态 0 未申报 1 已申报 2已驳回 3 审核通过';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_first" IS '文件类型code1';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_sec" IS '文件类型code2';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_third" IS '文件类型code3';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_first_value" IS '文件类型取值1';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_sec_value" IS '文件类型取值2';
COMMENT ON COLUMN "t_law_approvalpro"."file_type_third_value" IS '文件类型取值3';
COMMENT ON COLUMN "t_law_approvalpro"."file_modality_type_sec" IS '申报的发文形式2code';
COMMENT ON COLUMN "t_law_approvalpro"."file_modality_type_sec_value" IS '申报的发文形式取值code';



CREATE TABLE "t_law_file_code"
(
    "id" BIGINT NOT NULL,
    "file_name" VARCHAR(200),
    "file_url" VARCHAR(255),
    "form_data_id" BIGINT,
    "ext_name" VARCHAR(255),
    "file_size" BIGINT,
    "CREATE_TIME" TIMESTAMP(0),
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(128),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    "current_link" VARCHAR(255),
    "current_link_status" char(1),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "SWOA", CLUSTERBTR) ;
COMMENT ON TABLE "t_law_file_code" IS '法规室二维码附件表';
COMMENT ON COLUMN "t_law_file_code"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "t_law_file_code"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "t_law_file_code"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "t_law_file_code"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "t_law_file_code"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "t_law_file_code"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "t_law_file_code"."ext_name" IS '后缀名称';
COMMENT ON COLUMN "t_law_file_code"."file_name" IS '文件名称';
COMMENT ON COLUMN "t_law_file_code"."file_size" IS '文件大小';
COMMENT ON COLUMN "t_law_file_code"."file_url" IS '文件路径';
COMMENT ON COLUMN "t_law_file_code"."form_data_id" IS '表单数据id及业务主键id';
COMMENT ON COLUMN "t_law_file_code"."id" IS '主键';
COMMENT ON COLUMN "t_law_file_code"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "t_law_file_code"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "t_law_file_code"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "t_law_file_code"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "t_law_file_code"."current_link" IS '当前环节';
COMMENT ON COLUMN "t_law_file_code"."current_link_status" IS '当前环节状态 0 进行中 1 结束 只有最后一个环节有结束';



CREATE TABLE "t_law_approvalpro_flow"
(
    "id" BIGINT NOT NULL,
    "form_data_id" BIGINT,
    "CREATE_TIME" TIMESTAMP(0),
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(128),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    "COMPANY_NAME" VARCHAR(32),
    "DEPARTMENT_NAME" VARCHAR(32),
    "type" char(1),
    NOT CLUSTER PRIMARY KEY("id"))  ;
COMMENT ON TABLE "t_law_approvalpro_flow" IS '集中立项操作记录表';
COMMENT ON COLUMN "t_law_approvalpro_flow"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "t_law_approvalpro_flow"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "t_law_approvalpro_flow"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "t_law_approvalpro_flow"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."form_data_id" IS '表单数据id及业务主键id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."id" IS '主键';
COMMENT ON COLUMN "t_law_approvalpro_flow"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "t_law_approvalpro_flow"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "t_law_approvalpro_flow"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "t_law_approvalpro_flow"."COMPANY_NAME" IS '单位名称';
COMMENT ON COLUMN "t_law_approvalpro_flow"."DEPARTMENT_NAME" IS '部门名称';
COMMENT ON COLUMN "t_law_approvalpro_flow"."type" IS '类型 1 新增 2 修改 3 驳回 4 审核通过';

-- 20240726  测试已发

--王夏   临时立项新增字段
alter table "t_law_approvalpro" add examine_time timestamp;
comment on column "t_law_approvalpro".examine_time is '审核时间';



alter table "t_law_approvalpro" add approval_type char(1);
comment on column "t_law_approvalpro".approval_type is '立项类型 1 集中立项 2临时立项';


alter table "t_law_approvalpro" add tem_approval_type varchar(10);
comment on column "t_law_approvalpro".tem_approval_type is '临时立项类型ceode 数据字典 lslxlx';

alter table "t_law_approvalpro" add tem_approval_type_value varchar(10);
comment on column "t_law_approvalpro".tem_approval_type_value is '临时立项类型值 数据字典值 lslxlx';


alter table "t_law_approvalpro" add tem_approval_type_sec varchar(10);
comment on column "t_law_approvalpro".tem_approval_type_sec is '临时立项子类型code 数据字典 临时立项类型ceode找查找字典';

alter table "t_law_approvalpro" add tem_approval_type_sec_value varchar(10);
comment on column "t_law_approvalpro".tem_approval_type_sec_value is '临时立项子类型值 ';


alter table "t_law_approvalpro" add duration_classification varchar(256);
comment on column "t_law_approvalpro".duration_classification is '立项密级';


alter table "t_law_approvalpro" add duration_classification_name varchar(256);
comment on column "t_law_approvalpro".duration_classification_name is '立项密级值';

alter table "t_law_approvalpro" add file_seq_type varchar(10);
comment on column "t_law_approvalpro".file_seq_type is '所属序列   字典 ssxl';

alter table "t_law_approvalpro" add file_seq_type_value varchar(10);
comment on column "t_law_approvalpro".file_seq_type_value is '所属序列 ';

alter table "t_law_approvalpro" add file_aim varchar(2000);
comment on column "t_law_approvalpro".file_aim is '发文目的';

alter table "t_law_approvalpro" add code_id BIGINT;
comment on column "t_law_approvalpro".code_id is '二维码id';

--立项起草新增表  王夏

CREATE TABLE "t_law_approval_draft"
(
    "id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(32),
    "create_time" TIMESTAMP(0),
    "declare_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "department_name" VARCHAR(32),
    "company_id" BIGINT,
    "company_name" VARCHAR(32),
    "tenant_id" BIGINT,
    "deleted" INT,
    "approval_file_name" VARCHAR(512),
    "duration_classification" VARCHAR(256),
    "duration_classification_name" VARCHAR(256),
    "file_modality_type_sec" VARCHAR(15),
    "file_modality_type_sec_value" VARCHAR(64),
    "code_id" BIGINT,
    "approval_id" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")
) ;

COMMENT ON TABLE "t_law_approval_draft" IS '法规室立项文件起草';
COMMENT ON COLUMN "t_law_approval_draft"."id" IS '主键ID';
COMMENT ON COLUMN "t_law_approval_draft"."approval_file_name" IS '申报的文件名';
COMMENT ON COLUMN "t_law_approval_draft"."company_id" IS '单位ID';
COMMENT ON COLUMN "t_law_approval_draft"."company_name" IS '单位名称';
COMMENT ON COLUMN "t_law_approval_draft"."contact_person" IS '联系人';
COMMENT ON COLUMN "t_law_approval_draft"."contact_phone" IS '联系方式';
COMMENT ON COLUMN "t_law_approval_draft"."create_by" IS '起草人id';
COMMENT ON COLUMN "t_law_approval_draft"."create_name" IS '起草人名称';
COMMENT ON COLUMN "t_law_approval_draft"."create_time" IS '起草时间';
COMMENT ON COLUMN "t_law_approval_draft"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "t_law_approval_draft"."department_id" IS '起草部门ID';
COMMENT ON COLUMN "t_law_approval_draft"."department_name" IS '起草部门名称';
COMMENT ON COLUMN "t_law_approval_draft"."duration_classification" IS '密级';
COMMENT ON COLUMN "t_law_approval_draft"."duration_classification_name" IS '密级值';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_type_sec" IS '申报的发文形式2code';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_type_sec_value" IS '申报的发文形式取值code';
COMMENT ON COLUMN "t_law_approval_draft"."code_id" IS '二维码id';
COMMENT ON COLUMN "t_law_approval_draft"."approval_id" IS '立项id';


--王夏  立项文件起草
CREATE TABLE "t_law_approval_draft"
(
    "id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(32),
    "create_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "department_name" VARCHAR(32),
    "company_id" BIGINT,
    "company_name" VARCHAR(32),
    "tenant_id" BIGINT,
    "deleted" INT,
    "duration_classification" VARCHAR(256),
    "duration_classification_name" VARCHAR(256),
    "approval_file_name" VARCHAR(512),
    "approval_time" TIMESTAMP(0),
    "file_modality_type" VARCHAR(10),
    "file_modality_value" VARCHAR(64),
    "file_modality_type_sec" VARCHAR(15),
    "file_modality_type_sec_value" VARCHAR(64),
    "tem_approval_type" varchar(10),
    "tem_approval_type_value" varchar(10),
    "code_id" BIGINT,
    "approval_id" BIGINT,
    "document_file_id" BIGINT,
    "dylz_file_id" VARCHAR(128),
    "fxpg_file_id" VARCHAR(128),
    "zqyj_file_id" VARCHAR(128),
    "zqyj_type" varchar(15),
    "zqyj_type_value" varchar(56),
    "hfhgsh_file_id" VARCHAR(128),
    "jttlyj_file_id" VARCHAR(128),
    "file_range_type" varchar(10),
    "file_range_type_value" varchar(10),
    "detail_file_id" VARCHAR(128),
    NOT CLUSTER PRIMARY KEY("id")
) ;
COMMENT ON TABLE "t_law_approval_draft" IS '法规室立项文件起草';
COMMENT ON COLUMN "t_law_approval_draft"."id" IS '主键ID';
COMMENT ON COLUMN "t_law_approval_draft"."approval_file_name" IS '申报的文件名';
COMMENT ON COLUMN "t_law_approval_draft"."company_id" IS '单位ID';
COMMENT ON COLUMN "t_law_approval_draft"."company_name" IS '单位名称';
COMMENT ON COLUMN "t_law_approval_draft"."create_by" IS '起草人id';
COMMENT ON COLUMN "t_law_approval_draft"."create_name" IS '起草人名称';
COMMENT ON COLUMN "t_law_approval_draft"."create_time" IS '起草时间';
COMMENT ON COLUMN "t_law_approval_draft"."update_by" IS '修改人';
COMMENT ON COLUMN "t_law_approval_draft"."update_name" IS '修改人';
COMMENT ON COLUMN "t_law_approval_draft"."update_time" IS '修改时间';
COMMENT ON COLUMN "t_law_approval_draft"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "t_law_approval_draft"."tenant_id" IS '租户id';
COMMENT ON COLUMN "t_law_approval_draft"."department_id" IS '起草部门ID';
COMMENT ON COLUMN "t_law_approval_draft"."department_name" IS '起草部门名称';
COMMENT ON COLUMN "t_law_approval_draft"."duration_classification" IS '密级';
COMMENT ON COLUMN "t_law_approval_draft"."duration_classification_name" IS '密级值';
COMMENT ON COLUMN "t_law_approval_draft"."approval_time" IS '立项审核通过时间';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_type" IS '申报的规格code';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_value" IS '申报的发文规格取值';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_type_sec" IS '申报的规格形式2code';
COMMENT ON COLUMN "t_law_approval_draft"."file_modality_type_sec_value" IS '申报的发文规格取值code';
COMMENT ON COLUMN "t_law_approval_draft"."code_id" IS '二维码id';
COMMENT ON COLUMN "t_law_approval_draft"."approval_id" IS '立项id';
COMMENT ON COLUMN "t_law_approval_draft"."document_file_id" IS '正文id';
COMMENT ON COLUMN "t_law_approval_draft"."dylz_file_id" IS '调用论证文件id';
COMMENT ON COLUMN "t_law_approval_draft"."fxpg_file_id" IS '风险评估文件id';
COMMENT ON COLUMN "t_law_approval_draft"."zqyj_file_id" IS '征求意见文件id';
COMMENT ON COLUMN "t_law_approval_draft"."zqyj_type" IS '征求意见 数据字典';
COMMENT ON COLUMN "t_law_approval_draft"."zqyj_type_value" IS '征求意见值';
COMMENT ON COLUMN "t_law_approval_draft"."hfhgsh_file_id" IS '合法合规性审核id';
COMMENT ON COLUMN "t_law_approval_draft"."jttlyj_file_id" IS '集体讨论研究id';
COMMENT ON COLUMN "t_law_approval_draft"."file_range_type" IS '发文范围code';
COMMENT ON COLUMN "t_law_approval_draft"."file_range_type_value" IS '发文范围值';
COMMENT ON COLUMN "t_law_approval_draft"."detail_file_id" IS '资料id';
COMMENT ON COLUMN "t_law_approval_draft"."tem_approval_type" IS '立项类型';
COMMENT ON COLUMN "t_law_approval_draft"."tem_approval_type_value" IS '立项类型值';

alter table "cscp_enclosure_file" add lx_type varchar(56);
comment on column "cscp_enclosure_file".lx_type is '立项文件 需要不同类型区分';


--王夏 前置审核表 20240802
CREATE TABLE "t_law_approval_pre_audit"
(
    "id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(32),
    "create_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "department_name" VARCHAR(32),
    "company_id" BIGINT,
    "company_name" VARCHAR(32),
    "tenant_id" BIGINT,
    "deleted" INT,
    "approval_file_name" VARCHAR(512),
    "duration_classification" VARCHAR(256),
    "duration_classification_name" VARCHAR(256),
    "file_modality_type" VARCHAR(15),
    "file_modality_value" VARCHAR(64),
    "file_modality_type_sec" VARCHAR(15),
    "file_modality_type_sec_value" VARCHAR(64),
    "code_id" BIGINT,
    "approval_id" BIGINT,
    "hfxsc_status" char(1),
    "ggps_status" char(1),
    "status" BIGINT,
    "is_ggwj" char(1),
    "hfxsc_file_id" varchar(128),
    "ggps_file_id" varchar(128),
    "exaim_description" varchar(1024),
    "zl_file_id" varchar(128),
    "dwb_exaim_status" char(1),
    "dwb_exaim_content" varchar(1024),
    "dwb_zl_file_id" varchar(128),
    "zfb_exaim_status" char(1),
    "zfb_exaim_content" varchar(1024),
    "zfb_user_id" BIGINT,
    "zfb_zl_file_id" varchar(128),
    "dwb_exaim_time" TIMESTAMP(0),
    "zfb_exaim_time" TIMESTAMP(0),
    "dwb_user_id" BIGINT,
    "zfb_view" char(1),
    "dwb_view" char(1),
    NOT CLUSTER PRIMARY KEY("id")
) ;
COMMENT ON TABLE "t_law_approval_pre_audit" IS '法规室立项前置审核';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zl_file_id" IS '起草单位上传资料ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."hfxsc_status" IS '合法性检查状态 1是0否' ;
COMMENT ON COLUMN "t_law_approval_pre_audit"."ggps_status" IS '改革评审状态1是0否';
COMMENT ON COLUMN "t_law_approval_pre_audit"."status" IS '前置审核状态 0待审核 1审核中 2驳回 3审核通过';
COMMENT ON COLUMN "t_law_approval_pre_audit"."is_ggwj" IS '是否为改革类文件0否 1 是';
COMMENT ON COLUMN "t_law_approval_pre_audit"."ggps_file_id" IS '改革文件ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."hfxsc_file_id" IS '合法性和公平竞争审查文件ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."exaim_description" IS '采纳情况说明';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zl_file_id" IS '起草单位上传资料ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_exaim_status" IS '党委办审核状态 0 未审核 1驳回 2 审核通过' ;
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_exaim_content" IS '党委办审核以及说明';
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_zl_file_id" IS '党委办审核资料ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_exaim_status" IS '政府办审核状态 0 未审核 1驳回 2 审核通过';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_exaim_content" IS '政府办审核意见说明';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_user_id" IS '政府办审核人员id';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_zl_file_id" IS '政府办审核资料文件ids';
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_exaim_time" IS '党委办审核时间';
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_user_id" IS '党委办审核人id';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_exaim_time" IS '政府办审核时间';
COMMENT ON COLUMN "t_law_approval_pre_audit"."zfb_view" IS '政府办是否能看到数据0 否1 是';
COMMENT ON COLUMN "t_law_approval_pre_audit"."dwb_view" IS '党委办是否能看数据 0 1';
COMMENT ON COLUMN "t_law_approval_pre_audit"."id" IS '主键ID';
COMMENT ON COLUMN "t_law_approval_pre_audit"."approval_file_name" IS '申报的文件名';
COMMENT ON COLUMN "t_law_approval_pre_audit"."company_id" IS '单位ID';
COMMENT ON COLUMN "t_law_approval_pre_audit"."company_name" IS '单位名称';
COMMENT ON COLUMN "t_law_approval_pre_audit"."create_by" IS '起草人id';
COMMENT ON COLUMN "t_law_approval_pre_audit"."create_name" IS '起草人名称';
COMMENT ON COLUMN "t_law_approval_pre_audit"."create_time" IS '起草时间';
COMMENT ON COLUMN "t_law_approval_pre_audit"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "t_law_approval_pre_audit"."department_id" IS '起草部门ID';
COMMENT ON COLUMN "t_law_approval_pre_audit"."department_name" IS '起草部门名称';
COMMENT ON COLUMN "t_law_approval_pre_audit"."duration_classification" IS '密级';
COMMENT ON COLUMN "t_law_approval_pre_audit"."duration_classification_name" IS '密级值';
COMMENT ON COLUMN "t_law_approval_pre_audit"."file_modality_type" IS '申报的发文规格code';
COMMENT ON COLUMN "t_law_approval_pre_audit"."file_modality_value" IS '申报的发规格取值';
COMMENT ON COLUMN "t_law_approval_pre_audit"."file_modality_type_sec" IS '申报的发文规格2code';
COMMENT ON COLUMN "t_law_approval_pre_audit"."file_modality_type_sec_value" IS '申报的发规格取值';
COMMENT ON COLUMN "t_law_approval_pre_audit"."code_id" IS '二维码id';
COMMENT ON COLUMN "t_law_approval_pre_audit"."approval_id" IS '立项id';


--王夏 20240805 添加所属序列
alter table "t_law_approval_pre_audit" add file_seq_type varchar(10);
comment on column "t_law_approval_pre_audit".file_seq_type is '所属序列   字典 ssxl';

alter table "t_law_approval_pre_audit" add file_seq_type_value varchar(10);
comment on column "t_law_approval_pre_audit".file_seq_type_value is '所属序列 ';

alter table "t_law_approval_draft" add file_seq_type varchar(10);
comment on column "t_law_approval_draft".file_seq_type is '所属序列   字典 ssxl';

alter table "t_law_approval_draft" add file_seq_type_value varchar(10);
comment on column "t_law_approval_draft".file_seq_type_value is '所属序列 ';


alter table "t_law_approval_pre_audit" add dwb_exaim_company_name varchar(256);
comment on column "t_law_approval_pre_audit".dwb_exaim_company_name is '党委办审批单位名称--用于流程显示 ';

alter table "t_law_approval_pre_audit" add zfb_exaim_company_name varchar(256);
comment on column "t_law_approval_pre_audit".zfb_exaim_company_name is '政府办审批单位名称--用于流程显示 ';

alter table "t_law_approval_draft" add is_update_file_name char(1);
comment on column "t_law_approval_draft".is_update_file_name is '文件名是否修改 ';

alter table "t_law_approval_pre_audit" add is_update_file_name char(1);
comment on column "t_law_approval_pre_audit".is_update_file_name is '文件名是否修改 ';


alter table "t_law_approval_draft" add draft_time TIMESTAMP(0);
comment on column "t_law_approval_draft".draft_time is '起草时间 ';

alter table "t_law_approval_pre_audit" add draft_time TIMESTAMP(0);
comment on column "t_law_approval_pre_audit".draft_time is '起草时间 ';


alter table "t_law_approval_pre_audit" add create_exaim_time TIMESTAMP(0);
comment on column "t_law_approval_pre_audit".create_exaim_time is '起草单位审核时间 ';






-- 20240823  文件制定 - 督查落实及归档存档表

CREATE TABLE "t_law_file_supervision"
(
"id" BIGINT NOT NULL,
"code_id" BIGINT NOT NULL,
"approval_id" BIGINT,
"title" VARCHAR(100),
"supervision_status" INT DEFAULT 0,
"to_file_status" INT DEFAULT 0,
"supervision_id" BIGINT,
"duration_classification" VARCHAR(256),
"duration_classification_name" VARCHAR(256) NOT NULL,
"print_date" TIMESTAMP(6),
"create_by" BIGINT,
"create_name" VARCHAR(50),
"create_time" TIMESTAMP(6),
"update_by" BIGINT,
"update_name" VARCHAR(50),
"update_time" TIMESTAMP(6),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT DEFAULT 0,
"report_date" TIMESTAMP(6),
"place_company_name" VARCHAR(50),
"supervision_company_name" VARCHAR(50),
"place_time" TIMESTAMP(6),
NOT CLUSTER PRIMARY KEY("id"))  ;

COMMENT ON COLUMN "t_law_file_supervision"."approval_id" IS '立项id';
COMMENT ON COLUMN "t_law_file_supervision"."code_id" IS '文件二维码Id';
COMMENT ON COLUMN "t_law_file_supervision"."duration_classification" IS '密级期限code';
COMMENT ON COLUMN "t_law_file_supervision"."duration_classification_name" IS '密级期限名称';
COMMENT ON COLUMN "t_law_file_supervision"."id" IS '主键';
COMMENT ON COLUMN "t_law_file_supervision"."place_company_name" IS '归档单位';
COMMENT ON COLUMN "t_law_file_supervision"."place_time" IS '归档时间';
COMMENT ON COLUMN "t_law_file_supervision"."print_date" IS '印发时间';
COMMENT ON COLUMN "t_law_file_supervision"."report_date" IS '报备时间';
COMMENT ON COLUMN "t_law_file_supervision"."supervision_company_name" IS '发起督查单位';
COMMENT ON COLUMN "t_law_file_supervision"."supervision_id" IS '督查id';
COMMENT ON COLUMN "t_law_file_supervision"."supervision_status" IS '督查状态 0 未督查 1督查中 2 督查成功 3 督查失败';
COMMENT ON COLUMN "t_law_file_supervision"."title" IS '文件名称';
COMMENT ON COLUMN "t_law_file_supervision"."to_file_status" IS '归档状态 0 未归档 1已归档';










-- 20240823  文件制定 - 上报备案表

CREATE TABLE "t_law_file_report_high"
(
"id" BIGINT NOT NULL,
"code_id" BIGINT NOT NULL,
"approval_id" BIGINT,
"title" VARCHAR(100),
"duration_classification" VARCHAR(256),
"duration_classification_name" VARCHAR(256),
"duration_year" VARCHAR(10),
"draft_company_id" BIGINT,
"draft_department_id" BIGINT,
"print_date" TIMESTAMP(6),
"end_date" TIMESTAMP(6),
"report_date" TIMESTAMP(6),
"form_data_id" BIGINT,
"PROCESS_INSTANCE_ID" BIGINT,
"BPM_STATUS" INT,
"REMARK" VARCHAR(4000),
"DEPARTMENT_HEAD_NAME" VARCHAR(1000),
"lw_department_name" VARCHAR(200),
"lw_date" VARCHAR(50),
"lw_num" VARCHAR(50),
"lw_sort_num" VARCHAR(50),
"lw_type" VARCHAR(50),
"leader_opinion" VARCHAR(500),
"province_leader_opinion" VARCHAR(500),
"print_range" VARCHAR(50),
"secret_base" VARCHAR(50),
"urgent_level" VARCHAR(50),
"depart_man" VARCHAR(50),
"audit_man" VARCHAR(50),
"corss_man" VARCHAR(50),
"proposed_opinions" VARCHAR(500),
"share" VARCHAR(50),
"department_name" VARCHAR(50),
"company_name" VARCHAR(50),
"create_by" BIGINT,
"create_name" VARCHAR(50),
"create_time" TIMESTAMP(6),
"update_by" BIGINT,
"update_name" VARCHAR(50),
"update_time" TIMESTAMP(6),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT DEFAULT 0,
"submit" INT DEFAULT 0,
"oa_title" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("id"))  ;

COMMENT ON TABLE "t_law_file_report_high" IS '上报备案表';
COMMENT ON COLUMN "t_law_file_report_high"."approval_id" IS '立项id';
COMMENT ON COLUMN "t_law_file_report_high"."audit_man" IS '审核';
COMMENT ON COLUMN "t_law_file_report_high"."BPM_STATUS" IS '流程状态';
COMMENT ON COLUMN "t_law_file_report_high"."code_id" IS '文件二维码Id';
COMMENT ON COLUMN "t_law_file_report_high"."corss_man" IS '经办人';
COMMENT ON COLUMN "t_law_file_report_high"."depart_man" IS '处室负责人';
COMMENT ON COLUMN "t_law_file_report_high"."DEPARTMENT_HEAD_NAME" IS '处室负责人';
COMMENT ON COLUMN "t_law_file_report_high"."draft_company_id" IS '起草单位id';
COMMENT ON COLUMN "t_law_file_report_high"."draft_department_id" IS '起草部门id';
COMMENT ON COLUMN "t_law_file_report_high"."duration_classification" IS '密级期限code';
COMMENT ON COLUMN "t_law_file_report_high"."duration_classification_name" IS '密级期限名称';
COMMENT ON COLUMN "t_law_file_report_high"."duration_year" IS '保密期限';
COMMENT ON COLUMN "t_law_file_report_high"."end_date" IS '截止日期';
COMMENT ON COLUMN "t_law_file_report_high"."form_data_id" IS '流程formDateId';
COMMENT ON COLUMN "t_law_file_report_high"."id" IS '主键';
COMMENT ON COLUMN "t_law_file_report_high"."leader_opinion" IS '厅领导意见';
COMMENT ON COLUMN "t_law_file_report_high"."lw_date" IS '来文日期';
COMMENT ON COLUMN "t_law_file_report_high"."lw_department_name" IS '来文单位';
COMMENT ON COLUMN "t_law_file_report_high"."lw_num" IS '来文/发文字号';
COMMENT ON COLUMN "t_law_file_report_high"."lw_sort_num" IS '序号';
COMMENT ON COLUMN "t_law_file_report_high"."lw_type" IS '类别';
COMMENT ON COLUMN "t_law_file_report_high"."oa_title" IS 'OA标题';
COMMENT ON COLUMN "t_law_file_report_high"."print_date" IS '打印日期';
COMMENT ON COLUMN "t_law_file_report_high"."print_range" IS '印发范围';
COMMENT ON COLUMN "t_law_file_report_high"."PROCESS_INSTANCE_ID" IS '流程实例';
COMMENT ON COLUMN "t_law_file_report_high"."proposed_opinions" IS '拟办意见';
COMMENT ON COLUMN "t_law_file_report_high"."province_leader_opinion" IS '省领导批示';
COMMENT ON COLUMN "t_law_file_report_high"."REMARK" IS '备注';
COMMENT ON COLUMN "t_law_file_report_high"."report_date" IS '报备日期';
COMMENT ON COLUMN "t_law_file_report_high"."secret_base" IS '定密依据';
COMMENT ON COLUMN "t_law_file_report_high"."share" IS '处室共享';
COMMENT ON COLUMN "t_law_file_report_high"."title" IS '文件名称';
COMMENT ON COLUMN "t_law_file_report_high"."urgent_level" IS '紧急程度';







-- 20240823  文件制定 - 审核审发及印制发布表

CREATE TABLE "t_law_file_examine_print"
(
"id" BIGINT NOT NULL,
"code_id" BIGINT,
"title" VARCHAR(100),
"file_modality_type" VARCHAR(10),
"file_modality_type_sec" VARCHAR(15),
"file_modality_type_sec_value" VARCHAR(64),
"file_modality_value" VARCHAR(64),
"print_date" TIMESTAMP(6),
"filling" INT,
"duration_classification" VARCHAR(256),
"duration_classification_name" VARCHAR(256) NOT NULL,
"PROCESS_INSTANCE_ID" BIGINT,
"BPM_STATUS" INT,
"REMARK" VARCHAR(4000),
"DEPARTMENT_HEAD_NAME" VARCHAR(1000),
"create_by" BIGINT,
"create_name" VARCHAR(50),
"create_time" TIMESTAMP(6),
"update_by" BIGINT,
"update_name" VARCHAR(50),
"update_time" TIMESTAMP(6),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT DEFAULT 0,
"draft_company_id" BIGINT,
"draft_department_id" BIGINT,
"approval_id" BIGINT,
"form_data_id" BIGINT,
"lw_department_name" VARCHAR(200),
"lw_date" VARCHAR(50),
"lw_num" VARCHAR(50),
"lw_sort_num" VARCHAR(50),
"lw_type" VARCHAR(50),
"leader_opinion" VARCHAR(500),
"province_leader_opinion" VARCHAR(500),
"print_range" VARCHAR(50),
"secret_base" VARCHAR(50),
"urgent_level" VARCHAR(50),
"depart_man" VARCHAR(50),
"audit_man" VARCHAR(50),
"corss_man" VARCHAR(50),
"proposed_opinions" VARCHAR(500),
"share" VARCHAR(50),
"department_name" VARCHAR(50),
"company_name" VARCHAR(50),
"oa_title" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("id")) ;

COMMENT ON TABLE "t_law_file_examine_print" IS '审核审发及印制发布表';
COMMENT ON COLUMN "t_law_file_examine_print"."approval_id" IS '立项id';
COMMENT ON COLUMN "t_law_file_examine_print"."audit_man" IS '审核';
COMMENT ON COLUMN "t_law_file_examine_print"."BPM_STATUS" IS '流程状态';
COMMENT ON COLUMN "t_law_file_examine_print"."code_id" IS '文件二维码Id';
COMMENT ON COLUMN "t_law_file_examine_print"."corss_man" IS '经办人';
COMMENT ON COLUMN "t_law_file_examine_print"."depart_man" IS '处室负责人';
COMMENT ON COLUMN "t_law_file_examine_print"."DEPARTMENT_HEAD_NAME" IS '处室负责人';
COMMENT ON COLUMN "t_law_file_examine_print"."draft_company_id" IS '起草单位id';
COMMENT ON COLUMN "t_law_file_examine_print"."draft_department_id" IS '起草部门id';
COMMENT ON COLUMN "t_law_file_examine_print"."duration_classification" IS '密级期限code';
COMMENT ON COLUMN "t_law_file_examine_print"."duration_classification_name" IS '密级期限名称';
COMMENT ON COLUMN "t_law_file_examine_print"."file_modality_type" IS '申报的发文形式code';
COMMENT ON COLUMN "t_law_file_examine_print"."file_modality_type_sec" IS '申报的发文形式2code';
COMMENT ON COLUMN "t_law_file_examine_print"."file_modality_type_sec_value" IS '申报的发文形式2value';
COMMENT ON COLUMN "t_law_file_examine_print"."file_modality_value" IS '申报的发文形式value';
COMMENT ON COLUMN "t_law_file_examine_print"."filling" IS '上报备案';
COMMENT ON COLUMN "t_law_file_examine_print"."form_data_id" IS '流程formDateId';
COMMENT ON COLUMN "t_law_file_examine_print"."leader_opinion" IS '厅领导意见';
COMMENT ON COLUMN "t_law_file_examine_print"."lw_date" IS '来文日期';
COMMENT ON COLUMN "t_law_file_examine_print"."lw_department_name" IS '来文单位';
COMMENT ON COLUMN "t_law_file_examine_print"."lw_num" IS '来文/发文字号';
COMMENT ON COLUMN "t_law_file_examine_print"."lw_sort_num" IS '序号';
COMMENT ON COLUMN "t_law_file_examine_print"."lw_type" IS '类别';
COMMENT ON COLUMN "t_law_file_examine_print"."oa_title" IS 'oa标题';
COMMENT ON COLUMN "t_law_file_examine_print"."print_date" IS '打印日期';
COMMENT ON COLUMN "t_law_file_examine_print"."print_range" IS '印发范围';
COMMENT ON COLUMN "t_law_file_examine_print"."PROCESS_INSTANCE_ID" IS '流程实例';
COMMENT ON COLUMN "t_law_file_examine_print"."proposed_opinions" IS '拟办意见';
COMMENT ON COLUMN "t_law_file_examine_print"."province_leader_opinion" IS '省领导批示';
COMMENT ON COLUMN "t_law_file_examine_print"."REMARK" IS '备注';
COMMENT ON COLUMN "t_law_file_examine_print"."secret_base" IS '定密依据';
COMMENT ON COLUMN "t_law_file_examine_print"."share" IS '处室共享';
COMMENT ON COLUMN "t_law_file_examine_print"."title" IS '文件名称';
COMMENT ON COLUMN "t_law_file_examine_print"."urgent_level" IS '紧急程度';









-- 20240823  文件制定 - 审议批准表

CREATE TABLE "t_law_file_make_discussion_approve"
(
"id" BIGINT NOT NULL,
"code_id" BIGINT NOT NULL,
"title" VARCHAR(100),
"meeting_discuss_files" VARCHAR(300),
"org_discuss" INT DEFAULT 0,
"gov_discuss" INT DEFAULT 0,
"party_discuss" INT DEFAULT 0,
"party_all_discuss" INT DEFAULT 0,
"status" INT DEFAULT 0,
"duration_classification" VARCHAR(256),
"duration_classification_name" VARCHAR(256) NOT NULL,
"draft_time" TIMESTAMP(6),
"approve_time" TIMESTAMP(6),
"create_by" BIGINT,
"create_name" VARCHAR(50),
"create_time" TIMESTAMP(6),
"update_by" BIGINT,
"update_name" VARCHAR(50),
"update_time" TIMESTAMP(6),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT DEFAULT 0,
"approval_id" BIGINT,
"depart_party_discuss" INT DEFAULT 0,
"company_name" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("id"))  ;

COMMENT ON TABLE "t_law_file_make_discussion_approve" IS '文件制定审议批准表';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."approval_id" IS '立项id';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."approve_time" IS '批准时间';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."code_id" IS '文件二维码Id';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."company_name" IS '审批部门名称';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."depart_party_discuss" IS '部门单位党组（党委）审议';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."draft_time" IS '起草时间';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."duration_classification" IS '密级期限code';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."duration_classification_name" IS '密级期限名称';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."gov_discuss" IS '政府常务会议审议';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."id" IS '主键';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."meeting_discuss_files" IS '附件文件';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."org_discuss" IS '议事协调机构会议研讨';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."party_all_discuss" IS '党委全会审议';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."party_discuss" IS '党委常委会会议审议';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."status" IS '审议状态0未审议1已审议';
COMMENT ON COLUMN "t_law_file_make_discussion_approve"."title" IS '文件名称';