<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.approvalpro.mapper.TLawApprovalproMapper">


    <sql id="allfield">
        id,id as approval_id,create_by,create_name,create_time,declare_time,update_by,update_name,update_time,department_id,department_name,
        company_id,company_name,tenant_id,deleted,approval_file_name,contact_person,contact_phone,file_evidence,
        file_modality_type,file_modality_value,examine_department_id,examine_department_name,examine_company_id,
        examine_company_name,examine_id,examine_name,examine_contact_person,examine_contact_phone,status,
        advise_and_reason,examine_phone,file_type_first,file_type_sec,file_type_third,file_type_first_value,
        file_type_sec_value,file_type_third_value,file_modality_type_sec,file_modality_type_sec_value,file_code,
        publish_time,code_id,examine_time,approval_type,tem_approval_type,tem_approval_type_value,tem_approval_type_sec,
        tem_approval_type_sec_value,duration_classification,duration_classification_name,file_aim,file_seq_type_value,file_seq_type

    </sql>
    <update id="uploadFileName">
        update t_law_approvalpro set approval_file_name =#{approvalFileName} where id=#{id} and deleted=0
    </update>
    <select id="selectListPage" resultType="com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO">

     select
        <include refid="allfield"></include>

     from t_law_approvalpro
      <where>
          and deleted =0
          <if test="dto.approvalFileName != null and dto.approvalFileName != ''">
               and approval_file_name like concat('%',#{dto.approvalFileName},'%')
          </if>
          <if test="dto.startTime != null and dto.startTime != ''">
              and declare_time >= #{dto.startTime}
          </if>
          <if test="dto.endTime != null and dto.endTime != ''">
              and declare_time &lt;= #{dto.endTime}
          </if>
          <if test="dto.status != null and dto.status != ''">
              and status = #{dto.status}
          </if>
          <if test="dto.approvalType != null and dto.approvalType != ''">
              and approval_type = #{dto.approvalType}
          </if>
          <if test="dto.fileModalityType != null and dto.fileModalityType != ''">
              and file_modality_type = #{dto.fileModalityType}
          </if>
          <if test="dto.fileModalityTypeSec != null and dto.fileModalityTypeSec != ''">
              and file_modality_type_sec = #{dto.fileModalityTypeSec}
          </if>
          <if test="dto.createBy != null and dto.createBy != ''">
              and create_By = #{dto.createBy}
          </if>
          <if test="dto.temApprovalType != null and dto.temApprovalType != ''">
              and tem_approval_type = #{dto.temApprovalType}
          </if>

      </where>
    order by create_time desc
    </select>
    <select id="selectListByCondition" resultType="com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO">
        select
        a.id,a.id as approval_id,a.create_by,a.create_name,a.create_time,a.declare_time,a.update_by,a.update_name,a.update_time,
        a.department_id,a.department_name,a.company_id,a.company_name,a.tenant_id,a.deleted,a.publish_time,
               approval_file_name,contact_person,contact_phone,file_evidence,file_modality_type,
        code_id,examine_time,approval_type,tem_approval_type,tem_approval_type_value,tem_approval_type_sec,
        tem_approval_type_sec_value,duration_classification,duration_classification_name,file_aim,file_seq_type_value,file_seq_type,
        file_modality_value,examine_department_id,examine_department_name,
               examine_company_id,examine_company_name,examine_id,examine_name,
               examine_contact_person,examine_contact_phone,status,advise_and_reason,
               examine_phone,file_type_first,file_type_sec,file_type_third,
               file_type_first_value,file_type_sec_value,file_type_third_value,
               file_modality_type_sec,file_modality_type_sec_value,file_code,
               to_char(a.declare_time, 'yyyy-MM-dd') as declare_time_day,
               case when status=0 then '未申报'
                    when status=1 then '已申报'
                    when status=2 then '已驳回'
                    when status=3 then '审核通过'
                    else '' end as status_value,
        CONCAT(file_modality_value,'-',ifnull(file_modality_type_sec_value,''))  all_file_modality_value
        from t_law_approvalpro a
        left join cscp_org b on a.company_Id=b.id
        <where>
            and a.status >0 and a.deleted =0
            <if test="dto.approvalFileName != null and dto.approvalFileName != ''">
                and a.approval_file_name like concat('%',#{dto.approvalFileName},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and a.declare_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and a.declare_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.status != null and dto.status != ''">
                and a.status = #{dto.status}
            </if>
            <if test="dto.year != null and dto.year != ''">
                and year(a.declare_time) = #{dto.year}
            </if>

            <if test="dto.companyId != null and dto.companyId != ''">
                and a.company_Id = #{dto.companyId}
            </if>

            <if test="dto.temApprovalType != null and dto.temApprovalType != ''">
                and a.tem_approval_type = #{dto.temApprovalType}
            </if>

        </where>
        order by b.order_by
    </select>
    <select id="selectListAllPage" resultType="com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO">
        select
        a.id,a.id as approval_id,a.create_by,a.create_name,a.create_time,a.declare_time,a.update_by,a.update_name,a.update_time,
        a.department_id,a.department_name,a.company_id,a.company_name,a.tenant_id,a.deleted,a.publish_time,
        approval_file_name,contact_person,contact_phone,file_evidence,file_modality_type,
        file_modality_value,examine_department_id,examine_department_name,
        examine_company_id,examine_company_name,examine_id,examine_name,
        examine_contact_person,examine_contact_phone,status,advise_and_reason,
        examine_phone,file_type_first,file_type_sec,file_type_third,
        file_type_first_value,file_type_sec_value,file_type_third_value,
        file_modality_type_sec,file_modality_type_sec_value,file_code,
        case when status=0 then '未申报'
        when status=1 then '已申报'
        when status=2 then '已驳回'
        when status=3 then '审核通过'
        else '' end as status_value,
        code_id,examine_time,approval_type,tem_approval_type,tem_approval_type_value,tem_approval_type_sec,
        tem_approval_type_sec_value,duration_classification,duration_classification_name,file_aim,file_seq_type_value,file_seq_type


        from t_law_approvalpro a
        left join cscp_org b on a.company_Id=b.id
        <where>
            and a.status >0 and a.deleted =0
            <if test="dto.approvalFileName != null and dto.approvalFileName != ''">
                and a.approval_file_name like concat('%',#{dto.approvalFileName},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and a.declare_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and a.declare_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.status != null and dto.status != ''">
                and a.status = #{dto.status}
            </if>
            <if test="dto.year != null and dto.year != ''">
                and year(a.declare_time) = #{dto.year}
            </if>

            <if test="dto.companyId != null and dto.companyId != ''">
                and a.company_Id = #{dto.companyId}
            </if>

            <if test="dto.companyName != null and dto.companyName != ''">
                and a.company_Name like concat('%',#{dto.companyName},'%')
            </if>
            <if test="dto.approvalType != null and dto.approvalType != ''">
                and approval_type = #{dto.approvalType}
            </if>
            <if test="dto.fileModalityType != null and dto.fileModalityType != ''">
                and file_modality_type = #{dto.fileModalityType}
            </if>
            <if test="dto.fileModalityTypeSec != null and dto.fileModalityTypeSec != ''">
                and file_modality_type_sec = #{dto.fileModalityTypeSec}
            </if>
            <if test="dto.temApprovalType != null and dto.temApprovalType != ''">
                and tem_approval_type = #{dto.temApprovalType}
            </if>



        </where>
        order by b.order_by asc,status asc,create_time desc
    </select>
</mapper>