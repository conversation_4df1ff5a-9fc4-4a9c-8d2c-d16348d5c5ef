<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.approvalpro.mapper.TLawFileCodeMapper">


    <sql id="allfield">
        id,file_name,file_url,form_data_id,ext_name,file_size,CREATE_TIME,CREATE_BY,CREATE_NAME,UPDATE_TIME,UPDATE_BY,UPDATE_NAME,DEPARTMENT_ID,COMPANY_ID,TENANT_ID,DELETED,current_link,current_link_status

    </sql>
    <select id="selectByFormDataId" resultType="com.ctsi.approvalpro.vo.dto.TLawFileCodeDTO">
        select
           <include refid="allfield"></include>
        from t_law_file_code
        where DELETED =0 and form_data_id=#{formDataId} limit 1
    </select>
    <select id="getFlow" resultType="com.ctsi.approvalpro.vo.dto.TLawFlowDTO">
        select approval.create_time approval_create_time,
               approval.examine_time approval_examine_time,
               approval.company_name approval_company_name,
               approval.examine_company_name approval_examine_company_name,
               draft.draft_time draft_update_time,
               draft.company_name draft_company_name,
               paudit.company_name audit_company_name,
               paudit.create_exaim_time audit_time,
               paudit.dwb_exaim_time,
               paudit.dwb_exaim_company_name,
               paudit.zfb_exaim_time,
               paudit.zfb_exaim_company_name,
               disapprove.company_name as sypzCompanyName,
               disapprove.approve_time as sypzTime,
               eprint.company_name as printCompanyName,
               eprint.update_time as printTime,
               eprint.print_date as printDate,
               eprint.PROCESS_INSTANCE_ID as printProcessInstanceId,
               supervise.create_time as superviseTime,
               supervise.supervision_company_name as superviseCompanyName,
               supervise.place_time as placeTime,
               supervise.place_company_name as placeCompanyName,
               code.current_link,
               code.current_link_status
        from
            t_law_approvalpro approval
                left join t_law_approval_draft draft on approval.id=draft.approval_id and draft.deleted=0
                left join t_law_approval_pre_audit paudit on approval.id=paudit.approval_id and paudit.deleted=0
                left join t_law_file_make_discussion_approve disapprove on approval.id= disapprove.approval_id and disapprove.deleted=0
                left join t_law_file_examine_print eprint on approval.id=eprint.approval_id and eprint.deleted=0
                left join t_law_file_supervision supervise on approval.id=supervise.approval_id and supervise.deleted=0
                left join t_law_file_code code on approval.id=code.form_data_id and code.deleted=0
              where approval.id=#{id}  and approval.deleted=0    limit 1;
    </select>


</mapper>