<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.regulations.mapper.TRegulationsExaminationMapper">

    <select id="selectPageNoHandel" resultType="com.ctsi.regulations.entity.TRegulationsExamination">
        SELECT a.*
        FROM t_regulations_examination a
        WHERE NOT EXISTS (
        SELECT 1
        FROM t_regulations_handle b
        WHERE INSTR(b.EXAMINATION_ID, a.id) > 0
        )

        <if test="entityDTO.fileName != null and entityDTO.fileName != ''">
            AND file_name LIKE CONCAT('%', #{entityDTO.fileName}, '%')
        </if>
        <if test="entityDTO.startTime != null and entityDTO.endTime != null">
            AND reporting_time BETWEEN #{entityDTO.startTime} AND #{entityDTO.endTime}
        </if>
        <if test="entityDTO.reviewOpinions != null">
            AND review_opinions = #{entityDTO.reviewOpinions}
        </if>
        ORDER BY release_time DESC, create_time DESC

    </select>
</mapper>
