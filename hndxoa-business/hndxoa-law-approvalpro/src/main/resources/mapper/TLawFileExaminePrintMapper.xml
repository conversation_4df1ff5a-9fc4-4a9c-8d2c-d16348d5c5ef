<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.approvalpro.mapper.TLawFileExaminePrintMapper">

    <select id="queryListPageCount" resultType="java.lang.Integer">
        select count(1) from t_law_file_examine_print
        <where>
            deleted = 0
            and print_date is null
            <if test="entityDTO.title != null and entityDTO.title != ''">
                and title like CONCAT('%',#{entityDTO.title},'%')
            </if>
            <if test="entityDTO.fileModalityType != null">
                and file_modality_type = #{entityDTO.fileModalityType}
            </if>
            <if test="entityDTO.fileModalityTypeSec != null and entityDTO.fileModalityTypeSec != ''">
                and file_modality_type_sec = #{entityDTO.fileModalityTypeSec}
            </if>
            <if test="entityDTO.fileModalityTypeSecValue != null and entityDTO.fileModalityTypeSecValue != ''">
                and file_modality_type_sec_value like CONCAT('%',#{entityDTO.fileModalityTypeSecValue},'%')
            </if>
        </where>
    </select>

   <select id="queryListPage" resultType="com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo">
       select t1.*,base.bpm_status from t_law_file_examine_print t1 join cscp_org t2 on t1.draft_company_id = t2.id
       left join cscp_proc_base base on base.PROC_INST_ID = t1.PROCESS_INSTANCE_ID
       <where>
           t1.deleted = 0 and t2.deleted = 0
           and t1.print_date is null
           <if test="entityDTO.title != null and entityDTO.title != ''">
               and t1.title like CONCAT('%',#{entityDTO.title},'%')
           </if>
           <if test="entityDTO.fileModalityType != null">
               and t1.file_modality_type = #{entityDTO.fileModalityType}
           </if>
           <if test="entityDTO.fileModalityTypeSec != null and entityDTO.fileModalityTypeSec != ''">
               and t1.file_modality_type_sec = #{entityDTO.fileModalityTypeSec}
           </if>
           <if test="entityDTO.fileModalityTypeSecValue != null and entityDTO.fileModalityTypeSecValue != ''">
               and file_modality_type_sec_value like CONCAT('%',#{entityDTO.fileModalityTypeSecValue},'%')
           </if>
       </where>
       order by t2.order_by asc,base.bpm_status asc limit #{statrIndex},#{pageSize}
   </select>
</mapper>
