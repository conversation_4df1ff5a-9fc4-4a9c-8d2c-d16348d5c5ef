<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.approvalpro.mapper.TLawApprovalDraftMapper">


    <sql id="allfield">
        id,create_by,create_name,create_time,approval_time,update_by,update_name,update_time,department_id,
        department_name,company_id,company_name,tenant_id,deleted,approval_file_name,duration_classification,
        duration_classification_name,file_modality_type_sec,file_modality_type_sec_value,tem_approval_type,
        tem_approval_type_value,code_id,approval_id,document_file_id,dylz_file_id,fxpg_file_id,zqyj_file_id,zqyj_type,
        zqyj_type_value,hfhgsh_file_id,jttlyj_file_id,file_range_type,file_range_type_value,detail_file_id,
        file_modality_type,file_modality_value,status,file_seq_type_value,file_seq_type,is_update_file_name,draft_time

    </sql>
    <update id="removeByApprovalId">
        update t_law_approval_draft set deleted=1 where approval_id=#{approvalId} and deleted=0
    </update>
    <update id="uploadFileName">
        update t_law_approval_draft set approval_file_name=#{approvalFileName}  where approval_id =#{approvalId}
    </update>
    <select id="selectListPage" resultType="com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO">

        select
        <include refid="allfield"></include>

        from t_law_approval_draft
        <where>
            and deleted =0
            <if test="dto.approvalFileName != null and dto.approvalFileName != ''">
                and approval_file_name like concat('%',#{dto.approvalFileName},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and approval_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and approval_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.temApprovalType != null and dto.temApprovalType != ''">
                and tem_approval_type = #{dto.temApprovalType}
            </if>
            <if test="dto.fileModalityType != null and dto.fileModalityType != ''">
                and file_modality_type = #{dto.fileModalityType}
            </if>
            <if test="dto.fileModalityTypeSec != null and dto.fileModalityTypeSec != ''">
                and file_modality_type_sec = #{dto.fileModalityTypeSec}
            </if>
            <if test="dto.fileModalityTypeSecValue != null and dto.fileModalityTypeSecValue != ''">
                and file_modality_type_sec_value = #{dto.fileModalityTypeSecValue}
            </if>
            <if test="dto.fileRangeType != null and dto.fileRangeType != ''">
                and file_range_type = #{dto.fileRangeType}
            </if>

            <if test="dto.durationClassification != null and dto.durationClassification != ''">
                and duration_classification = #{dto.durationClassification}
            </if>
            <if test="dto.createBy != null and dto.createBy != ''">
                and create_By = #{dto.createBy}
            </if>
            <if test="dto.departmentId!= null and dto.departmentId != ''">
                and department_Id = #{dto.departmentId}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
