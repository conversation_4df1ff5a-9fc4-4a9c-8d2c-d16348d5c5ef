<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.approvalpro.mapper.TLawApprovalPreAuditMapper">

    <sql id="allfield">
        a.id,a.create_by,a.create_name,a.create_time,a.update_by,a.update_name,a.update_time,a.department_id,a.department_name,a.company_id,a.
        company_name,a.tenant_id,a.deleted,a.approval_file_name,a.duration_classification,a.duration_classification_name,a.file_modality_type,a.
        file_modality_value,a.file_modality_type_sec,a.file_modality_type_sec_value,a.code_id,a.approval_id,a.hfxsc_status,a.ggps_status,a.
        status,a.is_ggwj,a.hfxsc_file_id,a.ggps_file_id,a.exaim_description,a.zl_file_id,a.dwb_exaim_status,a.dwb_exaim_content,a.dwb_zl_file_id,a.
        zfb_exaim_status,a.zfb_exaim_content,a.zfb_user_id,a.zfb_zl_file_id,a.dwb_exaim_time,a.zfb_exaim_time,a.dwb_user_id,a.zfb_view,a.dwb_view,a.
        file_seq_type,a.file_seq_type_value,a.is_update_file_name,a.draft_time,a.create_exaim_time

    </sql>
    <update id="removeByApprovalId">
        update t_law_approval_pre_audit set deleted=1 where approval_id=#{approvalId}
    </update>
    <select id="queryListPageCondition" resultType="com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO">

        select
        <include refid="allfield"></include>

        from t_law_approval_pre_audit a
        left join cscp_org b on a.company_Id=b.id
        <where>
            and a.deleted =0
            <if test="dto.approvalFileName != null and dto.approvalFileName != ''">
                and a.approval_file_name like concat('%',#{dto.approvalFileName},'%')
            </if>

            <if test="dto.fileModalityType != null and dto.fileModalityType != ''">
                and a.file_modality_type = #{dto.fileModalityType}
            </if>



            <if test="dto.fileModalityTypeSec != null and dto.fileModalityTypeSec != ''">
                and a.file_modality_type_sec = #{dto.fileModalityTypeSec}
            </if>
            <if test="dto.fileModalityTypeSecValue != null and dto.fileModalityTypeSecValue != ''">
                and a.file_modality_type_sec_value = #{dto.fileModalityTypeSecValue}
            </if>
            <if test="dto.hfxscStatus != null and dto.hfxscStatus != ''">
                and a.hfxsc_status = #{dto.hfxscStatus}
            </if>
            <if test="dto.ggpsStatus != null and dto.ggpsStatus != ''">
                and a.ggps_status = #{dto.ggpsStatus}
            </if>

            <if test="dto.status != null and dto.status != ''">
                <if test="dto.status != 3 ">
                    and a.status = #{dto.status}
                </if>
                <if test="dto.status == 3 ">
                    and a.status >= #{dto.status}
                </if>
            </if>
            <if test="dto.companyId != null and dto.companyId != ''">
                and a.companyId = #{dto.companyId}
            </if>
            <if test="dto.companyName!= null and dto.companyName != ''">
                and a.company_name = #{dto.companyName}
            </if>
            <if test="dto.dwbView!= null and dto.dwbView != ''">
                and a.dwb_view = #{dto.dwbView}
            </if>
            <if test="dto.zfbView!= null and dto.zfbView != ''">
                and a.zfb_view = #{dto.zfbView}
            </if>
        </where>
        order by b.order_by asc,a.status asc,a.create_time desc
    </select>
</mapper>
