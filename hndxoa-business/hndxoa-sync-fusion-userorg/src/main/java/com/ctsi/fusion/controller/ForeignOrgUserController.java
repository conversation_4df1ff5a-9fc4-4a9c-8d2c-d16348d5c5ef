package com.ctsi.fusion.controller;

import com.ctsi.fusion.service.IForeignOrgUserService;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/foreign")
@Api(value = "对外提供机构用户数据接口")
@Slf4j
public class ForeignOrgUserController {

    @Autowired
    private IForeignOrgUserService foreignOrgUserService;


    @ApiOperation(value = "查询符合条件的所有的组织机构")
    @PostMapping("/cscpOrg/pull")
    @ShenyuSpringMvcClient(path = "/cscpOrg/pull", desc = "查询符合条件的所有的组织机构")
    public ResultVO<List<CscpOrgDTO>> selectPullCscpOrg(@RequestHeader Long appIdHeader, @RequestBody List<TYJGSyncUserOrgIdDTO> list) {
        List<CscpOrgDTO> result = foreignOrgUserService.selectPullCscpOrg(appIdHeader, list);
        return ResultVO.success(result);
    }


    @ApiOperation(value = "查询符合条件的所有的人员")
    @PostMapping("/cscpUser/pull")
    public ResultVO<List<CscpUserDTO>> selectPullCscpUser(@RequestBody List<TYJGSyncUserOrgIdDTO> list) {
        List<CscpUserDTO> result = foreignOrgUserService.selectPullCscpUser(list);
        return ResultVO.success(result);
    }

    @ApiOperation(value = "同步组织机构-回调写入推送记录")
    @PostMapping("/cscpOrg/callbackPull")
    public ResultVO callbackPullHistoryRecord(@RequestHeader String appCode, @RequestBody List<TYJGSyncUserOrgIdDTO> list) {
        foreignOrgUserService.callbackPullHistoryRecord(appCode, list);
        return ResultVO.success();
    }
}
