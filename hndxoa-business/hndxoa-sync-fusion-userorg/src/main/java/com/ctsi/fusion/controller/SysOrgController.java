package com.ctsi.fusion.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.fusion.common.SyncPushUtils;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.GzipUtil;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.CscpMainOrgVO;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.domain.vo.MainOrgVO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.annotation.ImpowerLog;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.ssdc.util.SpringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zdww.biyi.component.sdk.aop.BeanExposeMethodAble;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.ctsi.ssdc.admin.consts.ComponentConstant.ADMIN;
import static com.ctsi.ssdc.admin.consts.ComponentConstant.METHOD;

@RestController
@ResponseResultVo
@RequestMapping("/api/system")
@Api(value = "组织机构管理接口", tags = "组织机构管理接口")
@Slf4j
public class SysOrgController {

    private static final String ENTITY_NAME = "cscpOrg";

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SyncPushUtils syncPushUtils;

//    public CscpOrgController(CscpOrgService cscpOrgService) {
//        this.cscpOrgService = cscpOrgService;
//    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(true);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    @ApiOperation(value = "新增组织机构相关信息")
    @ImpowerLog(dBOperation = DBOperation.ADD, message = "")
    @PostMapping("/cscpOrgs/save")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增组织机构相关信息")
    public ResultVO<CscpOrgDTO> saveCscpOrg(@RequestBody CscpOrgDTO cscpOrgDTO) {
        CscpOrgDTO resultDTO = cscpOrgService.save(cscpOrgDTO);
        syncPushUtils.createSyncOrgToApp(resultDTO);
        return ResultVO.success(resultDTO);
    }

    @ApiOperation(value = "删除机构(机构ID)")
    @DeleteMapping("/cscpOrgs/{id}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除机构(机构ID)")
    public ResultVO deleteCscpOrg(@PathVariable Long id) {
        Long resultId = cscpOrgService.deleteOrgById(id);
//        syncPushUtils.syncBusiness(1,"delete", Collections.singletonList(resultId));
        return ResultVO.success();
    }

    @ApiOperation(value = "获取当前登录人账号所在的一些单位的")
    @GetMapping("/getCurrentOrg")
    public ResultVO<CscpOrgDTO> getCurrentOrg() {
        Long id = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();
        CscpOrg cscpOrg = cscpOrgService.getById(id);
        CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
        return ResultVO.success(cscpOrgDTO);
    }

    /**
     * 此接口用来解决，办公厅下面机要局，机要局下面信息中心，但是要取值信息中心
     * @return
     */
    @ApiOperation(value = "获取当前登录人账号的二级单位")
    @GetMapping("/getCurrentParentOrg")
    public ResultVO<CscpOrgDTO> getCurrentParentOrg() {
        CscpOrgDTO cscpOrgDTO = cscpOrgService.getCurrentParentOrg();
        return ResultVO.success(cscpOrgDTO);
    }


    @ApiOperation(value = "查询所有的组织机构和相关的工作组、人员")
    @GetMapping("/cscpOrgs/all")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<CscpOrgParamDTO> fetchCscpOrgs(Long parentId) {
        log.debug("REST request to get CscpOrgs");
        CscpOrgParamDTO cscpOrgParamDTO = cscpOrgService.fetchCscpOrgsUpdate(parentId);
        return ResultVO.success(cscpOrgParamDTO);
    }


    @ApiOperation(value = "更新机构(只更新机构信息)")
    @PutMapping("/updateCscpOrg")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新机构(只更新机构信息)")
    public ResultVO<List<Long>> updateCscpOrg(@RequestBody CscpOrgDTO cscpOrgDTO) throws URISyntaxException {
        log.debug("REST request to update CscpOrg : {}", cscpOrgDTO);
        if (cscpOrgDTO.getId() == null) {
            throw new BusinessException(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
        CscpOrg oldOrg = cscpOrgService.getById(cscpOrgDTO.getId());
        cscpOrgDTO.buildAddAppCodes(oldOrg.getPushAppCode());
        List<Long> t = cscpOrgService.update(cscpOrgDTO);
        syncPushUtils.updateSyncOrgToApp(cscpOrgDTO);
        return ResultVO.success(t);
    }


    /**
     * 处理指定parent_id下level为null的数据
     * @return 处理结果
     */
    @ApiOperation(value = "处理level为null的机构数据")
    @PostMapping("/dealNullLevel")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<Integer> processNullLevels() {
        Integer updatedCount = cscpOrgService.processNullLevels();
        return ResultVO.success();
    }

    @ApiOperation(value = "查询机构信息详情")
    @GetMapping("/cscpOrgs/{id}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<CscpOrgDTO> getCscpOrg(@PathVariable Long id) {
        log.debug("REST request to get CscpOrg : {}", id);
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(id);
        return ResultVO.success(cscpOrgDTO);
    }

    @ApiOperation(value = "查询多个机构信息详情")
    @GetMapping("/batch/cscpOrgs")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<List<CscpOrgDTO>> getCscpOrg(@RequestParam String ids) {
        log.debug("REST request to get CscpOrg : {}", ids);
        String[] list = ids.split(",");
        List<CscpOrgDTO> result = new ArrayList<>();
        for (String id : list) {
            CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(Long.valueOf(id));
            result.add(cscpOrgDTO);
        }

        return ResultVO.success(result);
    }


    @ApiOperation(value = "查询指定机构下的所有用户，部门信息")
    @GetMapping("/selectUserList/{id}")
    public ResultVO<List<CscpUserDTO>> selectUserList(@PathVariable(value = "id")
    Long id) {
        if (id == 0) {
            return ResultVO.success(Collections.EMPTY_LIST);
        }
        List<CscpUserDTO> cscpUserDTOS = cscpOrgService.selectUserlist(id);
        return ResultVO.success(cscpUserDTOS);
    }

    @ApiOperation(value = "分页查询指定机构下的所有用户，部门信息")
    @GetMapping("/pageQueryUserList/{id}")
    public ResultVO<PageResult<CscpUserDTO>> pageQueryUserList(@PathVariable(value = "id", required = true) Long id,
            @RequestParam(required = false) String realName,
            BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOS = cscpOrgService.pageQueryUserList(id, realName, null, basePageForm);
        return ResultVO.success(cscpUserDTOS);
    }
    @ApiOperation(value = "分页查询当前用户所在单位的用户")
    @GetMapping("/pageQueryCompanyUserList")
    public ResultVO<PageResult<CscpUserDTO>> pageQueryCompanyUserList(@RequestParam(required = false) String realName) {
        Long id = SecurityUtils.getCurrentCompanyId();
        BasePageForm basePageForm = new BasePageForm();
        basePageForm.setCurrentPage(1);
        basePageForm.setPageSize(1000);
        PageResult<CscpUserDTO> cscpUserDTOS = cscpOrgService.pageQueryUserList(id, realName, null, basePageForm);
        return ResultVO.success(cscpUserDTOS);
    }

    @ApiOperation(value = "分页查询指定机构下的所有用户，部门信息(不区分是否显示和是否停用)")
    @GetMapping("/pageSelectUsers/{id}")
    public ResultVO<PageResult<CscpUserDTO>> pageSelectUsers(@PathVariable(value = "id", required = true) Long id,
            @RequestParam(required = false) String realName,
            BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOS = cscpOrgService.pageSelectUsers(id, realName, basePageForm);
        return ResultVO.success(cscpUserDTOS);
    }

    @ApiOperation(value = "分页查询指定机构下的所有用户，部门信息(不区分是否显示和是否停用)")
    @PutMapping("/cscpOrg/userOrderBy")
    public ResultVO<Void> orgUserOrderBy(@RequestBody List<CscpUserDTO> list) {
        cscpUserOrgService.orgUserOrderBy(list);
        return ResultVO.success();
    }

    @ApiOperation(value = "查询部门id集成下面的的所有用户信息，根据用户名模糊查询")
    @PostMapping("/queryCscpUserByOrdIdsListIds")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<PageResult<CscpUserDTO>> queryCscpUserByOrdIdsListIds(@RequestBody CscpOtherListRealName cscpOtherListRealName,
            BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOList = cscpUserOrgService.queryCscpUserByOrdIdsListIds(cscpOtherListRealName, basePageForm);
        return ResultVO.success(cscpUserDTOList);
    }


    @ApiOperation(value = "查询所有机构信息,不需要勾选已经选择的节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
    })
    @GetMapping("/selectOrgList")
    public ResultVO<List<Node<CscpOrgDTO>>> selectOrgList(@RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId) {
        Node<CscpOrgDTO> parentNode = new Node<>();
        if (SecurityUtils.isGeneralName()) {
            parentId = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();
            CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(parentId);
            parentNode.setId(parentId);
            parentNode.setTitle(cscpOrgDTO.getOrgName());
            parentNode.setDataIsIntegrity(cscpOrgDTO.getDataIsIntegrity());
            parentNode.setDetailsData(cscpOrgDTO);
        }
        List<Node<CscpOrgDTO>> cscpTreeDataNode = cscpOrgService.selectChildrenListNodeByParentId(parentId);
        if (SecurityUtils.isGeneralName()) {
            parentNode.setChildren(cscpTreeDataNode);
            List<Node<CscpOrgDTO>> result = new ArrayList<>();
            result.add(parentNode);
            return ResultVO.success(result);
        } else {
            return ResultVO.success(cscpTreeDataNode);
        }

    }


    @ResponseResultVo
    @ApiOperation(value = "查询所有机构信息,勾选已经选择的节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
            @ApiImplicitParam(name = "ids", value = "已经选择的节点的id", allowMultiple = true,
                    dataType = "Long")
    })
    @PostMapping("/selectOrgListCheckedNode")
    public ResultVO<List<Node<CscpOrgDTO>>> selectOrgListCheckedNode(@RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
            @RequestBody List<Long> ids) {
        if (SecurityUtils.isGeneralName()) {
            parentId = cscpOrgService.getById(SecurityUtils.getCurrentCscpUserDetail().getCompanyId()).getParentId();
        }
        List<Node<CscpOrgDTO>> cscpTreeDataNode = cscpOrgService.selectChildrenListNodeByParentId(parentId, ids);
        return ResultVO.success(cscpTreeDataNode);
    }


    //  此接口暂时不再使用，使用上面的接口
 /*   @ResponseResultVo
    @ApiOperation(value = "查询本单位所有机构信息(限普通用户)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
            @ApiImplicitParam(name = "ids", value = "已经选择的节点的id", allowMultiple = true,
                    dataType = "Long")
    })
    @PostMapping("/selectThisUnitAll")
    public ResultVO<List<Node<CscpOrgDTO>>> selectThisUnitAll(@RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
                                                              @RequestBody List<Long> ids){
        if(SecurityUtils.isTenantName() || SecurityUtils.isSystemName()){
            throw new BusinessException("该接口仅限普通用户使用");
        }
        List<Node<CscpOrgDTO>> cscpThisUnitAll = cscpOrgService.seletThisUnitAllDepartment(parentId, ids);
        return ResultVO.success(cscpThisUnitAll);
    }*/


    @ResponseResultVo
    @ApiOperation(value = "只查询已经勾选的节点树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
            @ApiImplicitParam(name = "ids", value = "已经选择的节点的id", allowMultiple = true,
                    dataType = "Long")
    })
    @PostMapping("/listCheckedOrgNodeTree")
    public ResultVO<List<Node<CscpOrgDTO>>> listCheckedOrgNodeTree(@RequestParam(value = "parentId", required = true,
            defaultValue = "0") Long parentId, @RequestBody List<Long> ids) {
        List<Node<CscpOrgDTO>> cscpTreeDataNode = cscpOrgService.selectCheckedOrgNodeTree(parentId, ids);
        return ResultVO.success(cscpTreeDataNode);
    }


    /**
     * 查询租户下面的所有单位
     *
     * @param id
     * @return
     */
    @GetMapping("/selectTenantAllOrg/{id}")
    @ApiOperation(value = "查询租户下面的所有单位")
    public ResultVO<List<Node<CscpOrgDTO>>> selectTenantAllOrg(@PathVariable Long id) {
        List<Node<CscpOrgDTO>> nodesList = cscpOrgService.selectTenantOrgPage(id);
        return ResultVO.success(nodesList);
    }

    /**
     * 根据租户id 查询所有单位 平级展示所有单位信息
     *
     * @param tenantId
     * @return
     */
    @GetMapping("/selectAllCompany")
    @ApiOperation(value = "根据租户id 查询所有单位 平级展示所有单位信息")
    public ResultVO<List<CscpOrgDTO>> selectAllCompany(Long tenantId) {
        List<CscpOrgDTO> companyLst = cscpOrgService.selectAllCompany(tenantId);
        return ResultVO.success(companyLst);
    }

    @GetMapping("/selectAllCompanyByOrgName")
    @ApiOperation(value = "根据租户id 查询所有单位 平级展示所有单位信息")
    public ResultVO<List<CscpOrgDTO>> selectAllCompanyByOrgName(String orgName) {
        List<CscpOrgDTO> companyLst = cscpOrgService.selectAllCompanyByOrgName(orgName);
        return ResultVO.success(companyLst);
    }

    /**
     * 查询本租户下面的单位及其组织机构
     *
     * @param id
     * @return
     */
    @GetMapping("/selectOwerTenantAllOrg")
    @ApiOperation(value = "查询租户下面的所有单位")
    public ResultVO<List<Node<CscpOrgDTO>>> selectOwerTenantAllOrg() {
        List<Node<CscpOrgDTO>> nodesList = cscpOrgService.selectTenantOrgPage(SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        return ResultVO.success(nodesList);
    }


    /**
     * 查询租户下面的所有组织机构，包括单位、虚拟机构和部门
     *
     * @param id
     * @return
     */
    @GetMapping("/selectOrgTreeByTenantId/{id}")
    @ApiOperation(value = "查询租户下面的所有组织机构，包括单位、虚拟机构和部门")
    public ResultVO<List<Node<CscpOrgDTO>>> selectOrgTreeByTenantId(@PathVariable Long id) {
        List<Node<CscpOrgDTO>> nodeList = cscpOrgService.selectOrgTreeByTenantId(id);
        return ResultVO.success(nodeList);
    }


    /**
     * 根据id查询下级机构,id为0时查询所有顶层单位
     *
     * @param id
     * @param queryType all 显示所有 包含虚拟机构
     * @return
     */
    @GetMapping("/selectOrgById")
    @ApiOperation(value = "根据id查询下级机构,id为0时查询所有顶层单位")
    public ResultVO<List<CscpOrgDTO>> selectOrgById(Long id ,String queryType, String orgCodePath, String orgName) {
        List<CscpOrgDTO> cscpOrgDTOList = cscpOrgService.selectOrgById(id,queryType,orgCodePath,orgName);
        return ResultVO.success(cscpOrgDTOList);
    }

    /**
     * 根据机构id获取单位id
     * 如果传入的是部门id，则返回上级单位id
     * 如果传入的是单位id，则原值返回
     *
     * @param orgId 机构id
     * @return 单位id
     */
    @GetMapping("/getCompanyIdByOrgId/{orgId}")
    @ApiOperation(value = "根据机构id获取单位id")
    public ResultVO<Long> getUnitIdByOrgId(@PathVariable("orgId") Long orgId) {
        Long unitId = cscpOrgService.getUnitIdByOrgId(orgId);
        return ResultVO.success(unitId);
    }

    /**
     * 校验统一社会信用代码是否唯一
     * @param id
     * @param creditCode
     * @return
     */
    @GetMapping("/checkCreditCode")
    @ApiOperation(value = "校验统一社会信用代码是否唯一")
    public ResultVO<Boolean> checkCreditCode(Long id, String creditCode) {
        boolean flag = cscpOrgService.checkCreditCode(id, creditCode);
        return ResultVO.success(flag);
    }

    @GetMapping("/selectOrgCityState")
    @ApiOperation(value = "根据查询市州下数据")
    public ResultVO<List<CscpOrgDTO>> selectOrgCityState() {
        List<CscpOrgDTO> cscpOrgDTOList = cscpOrgService.selectOrgCityState();
        return ResultVO.success(cscpOrgDTOList);
    }

    /**
     * 查询湖南省所有市州的机构信息
     */
    @GetMapping("/selectOrgRegionCityList")
    @ApiOperation(value = "查询湖南省14个市州的机构列表")
    public ResultVO<List<CscpOrgDTO>> getHunanCityOrgs() {
        List<CscpOrgDTO> cityOrgList = cscpOrgService.selectOrgRegionCityList();
        return ResultVO.success(cityOrgList);
    }

    @GetMapping("/getOrgHierarchyByName")
    @ApiOperation(value = "根据机构名称查询机构及其所有父级")
    public ResultVO<List<CscpOrgDTO>> getOrgHierarchyByName(
            @RequestParam String orgName) {

        List<CscpOrgDTO> result = cscpOrgService.getOrgWithAllParents(orgName);
        return ResultVO.success(result);
    }

    @GetMapping("/getOrgHierarchyByNameNew")
    @ApiOperation(value = "根据机构名称查询机构及其所有父级")
    public ResultVO<List<CscpOrgDTO>> getOrgHierarchyByNameNew(
            @RequestParam String orgName) {
        List<CscpOrgDTO> result = cscpOrgService.getOrgWithAllParentsNew(orgName);
        return ResultVO.success(result);
    }

    @GetMapping("/getOrgHierarchyByNameNewest")
    @ApiOperation(value = "根据机构名称查询机构及其所有父级")
    public ResultVO<List<CscpOrgDTO>> getOrgHierarchyByNameNewest(
            @RequestParam String orgName, @RequestParam(required = false) Long orgId, @RequestParam(required = false) Integer allFlag) {
        List<CscpOrgDTO> result = cscpOrgService.getOrgWithAllParentsNewest(orgName, orgId, allFlag);
        return ResultVO.success(result);
    }

    @GetMapping("/selectOrgListById/{id}")
    @ApiOperation(value = "根据机构名称查询机构及其所有父级")
    public ResultVO<List<CscpOrgDTO>> selectOrgListById(@PathVariable Long id) {
        List<CscpOrgDTO> result = cscpOrgService.selectOrgListById(id);
        return ResultVO.success(result);
    }

    /**
     * 查询单位下级所有机构
     *
     * @param parentId
     * @param tenantId
     * @return
     */
    @GetMapping("/selectSubordinateAllOrg")
    @ApiOperation(value = "查询单位下级所有机构")
    public ResultVO<List<CscpOrgDTO>> selectSubordinateAllOrg(Long parentId, Long tenantId) {
        List<CscpOrgDTO> cscpOrgDTOS = cscpOrgService.selectSubordinateAllOrg(parentId, tenantId);
        return ResultVO.success(cscpOrgDTOS);
    }


    @ApiOperation(value = "只能使用在本租户或者本单位中查询所有机构信息,不需要勾选已经选择的节点，带用户名搜索") //querycondition  querycondition
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
            @ApiImplicitParam(name = "onlyDepart", value = "是否仅获取本部门,0-全单位，1-本部门", required = true)
    })
    @GetMapping("/selectOrgAndQueryRealNameList")
    public ResultVO<CscpOrgAndUserDto> selectOrgAndQueryRealNameList(@RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
            String realName,
            @RequestParam(value = "onlyDepart", defaultValue = "0") Integer onlyDepart) {
        return ResultVO.success(cscpOrgService.selectOrgAndQueryRealNameList(parentId, realName,onlyDepart));
    }


    /**
     * 分阅人群搜索
     */
    @GetMapping("/queryTenantUser")
    @ApiOperation(value = "分阅人群搜索")
    public ResultVO<PageResult<CscpUserDTO>> queryTenantUser(String realName, BasePageForm basePageForm) {
        return ResultVO.success(cscpOrgService.queryTenantUser(realName, basePageForm));
    }


    /**
     * 获取本单位下的用户信息
     */
    @GetMapping("/selectThisUnit")
    @ApiOperation(value = "获取本单位下的用户信息")
    public ResultVO<List<CscpUserDTO>> selectThisUnit() {
        return ResultVO.success(cscpOrgService.selectCompayAllUserByCompanyId(null));
    }


    /**
     * 移动端在流程办理中获取本单位下面所有人员信息，返回有格式区别上面接口
     */
    @PostMapping("/pageSelectThisUnit")
    @ApiOperation(value = "移动端在流程办理中获取本单位下面所有人员信息，返回有格式区别上面接口")
    public ResultVO<PageResult<CscpUserDTO>> pageSelectThisUnit(@RequestBody CscpOtherListRealName cscpOtherListRealName,
            BasePageForm basePageForm) {
        List<CscpUserDTO> userList = cscpOrgService.selectCompayAllUserByCompanyId(null);
        PageResult pageResult = new PageResult(userList, userList.size(), userList.size());
        return ResultVO.success(pageResult);
    }

    /**
     * 获取实际考核人数
     *
     * @return
     */
    @GetMapping("/selectAssessmentPeopleCount")
    @ApiOperation(value = "获取本单位下的用户信息")
    public ResultVO<List<CscpOrgDTO>> selectAssessmentPeopleCount() {
        return ResultVO.success(cscpOrgService.selectAssessmentPeopleCount());
    }

    /**
     * 修改实际考核人数
     *
     * @param assessmentPeopleCounts
     * @return
     */
    @PostMapping("/updateAssessmentPeopleCount")
    @ApiOperation(value = "修改实际考核人数")
    public ResultVO updateAssessmentPeopleCount(@RequestBody @Valid List<CscpAssessmentPeopleCountDTO> assessmentPeopleCounts) {
        Boolean aBoolean = cscpOrgService.updateAssessmentPeopleCount(assessmentPeopleCounts);
        if (aBoolean) {
            return ResultVO.success();
        } else {
            return ResultVO.error("修改失败!");
        }
    }

    /**
     * 获取单位的全宗号
     *
     * @return
     */
    @GetMapping("/getdossierNumber")
    @ApiOperation(value = "获取单位的全宗号")
    public ResultVO getdossierNumber() {
        String dossierNumber = null;
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(SecurityUtils.getCurrentCompanyId());
        dossierNumber = cscpOrgDTO.getDossierNumber();
        return ResultVO.success(dossierNumber);
    }


    @ApiOperation(value = "查询本租户下面所有单位, 可带上用户名称查询")
    @PostMapping("/selectTenantTreeOrgAndUser")
    public ResultVO<CscpOrgAndUserDto> selectTenantTreeOrgAndUser(@RequestBody CscpTenantOrgTreeQueryDTO req) {
        return ResultVO.success(cscpOrgService.selectTenantTreeOrgAndUser(req));
    }

    @ApiOperation(value = "生成单位管理员")
    @PostMapping("/createCompanyAdmin")
    public ResultVO createCompanyAdmin(@RequestParam(value = "orgCode", required = true) String orgCode) {
        cscpOrgService.createCompanyAdmin(orgCode);
        return ResultVO.success();
    }

    /**
     * 区划机构管理查询机构列表
     * @param id
     * @return
     */
    @GetMapping("/selectDivisionOrgById")
    @ApiOperation(value = "区划机构管理查询机构列表")
    public ResultVO<List<CscpOrgDTO>> selectDivisionOrgById(Long id, String orgCodePath) {
        //List<CscpOrgDTO> cscpOrgDTOList = cscpOrgService.selectDivisionOrgById(id, orgCodePath);
        List<CscpOrgDTO> cscpOrgDTOList = cscpOrgService.selectDivisionOrgById_new(id, orgCodePath);
        return ResultVO.success(cscpOrgDTOList);
    }

    @GetMapping("/selectDivisionOrgByIdName")
    @ApiOperation(value = "区划用户管理查询机构列表")
    public ResultVO<List<CscpOrgDTO>> selectDivisionOrgByIdName(@RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "orgName", required = false) String orgName) {
        List<CscpOrgDTO> cscpOrgDTOList = cscpOrgService.selectDivisionOrgByIdName(id, orgName);
        return ResultVO.success(cscpOrgDTOList);
    }

    @PostMapping("/updateOrgOrderBy")
    @ApiOperation(value = "批量修改机构排序")
    public ResultVO selectDivisionOrgByIdName(@RequestBody List<CscpOrgDTO> dto) {
        cscpOrgService.updateOrgOrderBy(dto);
        return ResultVO.success();
    }

    @ApiOperation(value = "初始化机构表idPath、codePath")
    @PostMapping("/init/path")
    public ResultVO<Void> initOrgPath() {
        try {
            log.info("开始初始化机构路径...");
            CompletableFuture.runAsync(() -> {
                try {
                    cscpOrgService.initOrgPath();
                    log.info("机构表路径初始化完成");
                } catch (Exception e) {
                    log.error("机构表路径初始化失败", e);
                }
            });
            return ResultVO.success("任务已提交");
        } catch (Exception e) {
            log.error("任务提交失败", e);
            return ResultVO.error("任务提交失败：" + e.getMessage());
        }
    }

    @PostMapping("/fixInvalidOrgCodes")
    @ApiOperation(value = "修复无效的机构编码", notes = "自动修复所有org_code等于id的机构编码")
    public ResultVO<String> fixInvalidOrgCodes() {
        try {
            cscpOrgService.updateInvalidOrgCodes();
            return ResultVO.success("机构编码修复完成，SQL语句已保存到日志文件");
        } catch (Exception e) {
            log.error("修复机构编码失败", e);
            return ResultVO.error("修复机构编码失败: " + e.getMessage());
        }
    }

    @PostMapping("/queryMainOrgTree")
    @ApiOperation(value = "查询商信组织结构树", notes = "传入参数")
    public ResultVO queryMainOrgTree() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        String json = (String) redisUtil.get("sys:org:tree");
        if(StrUtil.isNotEmpty(json)){
            JSONObject resStr = JSONObject.parseObject(GzipUtil.uncompress(json));
            return  ResultVO.success(resStr);
        }
        // 存缓存
        MainOrgVO res=cscpOrgService.queryMainOrgTree();
        String value = objectMapper.writeValueAsString(res);
        String compressMenuStr = GzipUtil.compress(value);
        redisUtil.set("sys:org:tree",compressMenuStr);

        return ResultVO.success(res);
    }

    @GetMapping("/queryUnitOrgTree")
    @ApiOperation(value = "查询单位管理员组织结构树", notes = "传入参数")
    public ResultVO<List<CscpMainOrgVO>> queryUnitOrgTree(@RequestParam(required = false, defaultValue = "0") Long parentId) {
        List<CscpMainOrgVO> list = cscpOrgService.queryUnitOrgTree(parentId);
        return ResultVO.success(list);
    }

    @GetMapping("/queryUnitOrgTreeByName")
    @ApiOperation(value = "查询单位管理员组织结构树", notes = "传入参数")
    public ResultVO<MainOrgVO> queryUnitOrgTreeByName(@RequestParam(required = false) String orgName) {
        MainOrgVO mainOrg = cscpOrgService.queryUnitOrgTreeByName(orgName);
        return ResultVO.success(mainOrg);
    }

    @PostMapping("/selectAllChildNodesListById")
    public ResultVO<List<CscpOrgDTO>> selectAllChildNodesListById(@RequestBody List<Long> ids)  {
        List<CscpOrgDTO> dtoList = cscpOrgService.selectAllChildNodesListById(ids);
        return ResultVO.success(dtoList);
    }






    @GetMapping("/selectDeletedList")
    public ResultVO<PageResult<CscpOrgDTO>> selectDeletedList(@RequestParam(required = false) String orgName,@RequestParam(required = false) String orgCode, BasePageForm basePageForm) {

        return ResultVO.success(cscpOrgService.selectDeletedList(orgName,orgCode,basePageForm));
    }
  @GetMapping("/deleteRealById")
    @ApiOperation(value = "物理删除", notes = "传入参数")
    public ResultVO<Boolean> deleteRealById(@RequestParam(required = true) Long id) {


        return ResultVO.success(cscpOrgService.deleteRealById(id));
    }

    @GetMapping("/getUserAdminAndCreditCodeStatus")
    @ApiOperation(value = "查询当前用户管理员状态及信用代码填写需求", notes = "返回用户是否为管理员及是否需要填写统一社会信用代码")
    public ResultVO<Map<String, Object>> getUserAdminAndCreditCodeStatus() {
        Map<String, Object> result = cscpOrgService.getUserAdminAndCreditCodeStatus();
        return ResultVO.success(result);
    }
    @PostMapping("/updateCreditCode")
    @ApiOperation(value = "更新单位统一社会信用代码", notes = "仅管理员可操作")
    public ResultVO<String> updateCreditCode(@RequestParam String creditCode) {
        try {
            boolean updateSuccess = cscpOrgService.updateCreditCode(creditCode);

            if (updateSuccess) {
                // 只有更新成功才进行推送
                ITSyncAppSystemManageService itSyncAppSystemManageService = SpringUtil.getBean(ITSyncAppSystemManageService.class);
                SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                syncOrgUserDTO.setIsAutoPushFlag(true);
                syncOrgUserDTO.setFlag("update");
                syncOrgUserDTO.setOrgId(SecurityUtils.getCurrentCompanyId());
                itSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);

                return ResultVO.success("统一社会信用代码更新成功");
            } else {
                return ResultVO.error("统一社会信用代码更新失败");
            }
        } catch (BusinessException e) {
            // 业务异常时不进行推送，直接返回错误信息
            return ResultVO.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新统一社会信用代码失败", e);
            // 其他异常时不进行推送，返回通用错误信息
            return ResultVO.error("统一社会信用代码更新失败");
        }
    }


}
