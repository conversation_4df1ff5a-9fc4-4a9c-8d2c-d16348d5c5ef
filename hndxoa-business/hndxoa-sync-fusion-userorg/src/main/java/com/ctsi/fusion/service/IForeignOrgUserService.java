package com.ctsi.fusion.service;

import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;

import java.util.List;

public interface IForeignOrgUserService {

    List<CscpOrgDTO> selectPullCscpOrg(String appCode, List<TYJGSyncUserOrgIdDTO> list);

    List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list);

    void callbackPullHistoryRecord(String appCode, List<TYJGSyncUserOrgIdDTO> list);
}
