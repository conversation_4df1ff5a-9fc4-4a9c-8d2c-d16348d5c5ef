package com.ctsi.service.impl;


import com.ctsi.domain.CformTypePermission;
import com.ctsi.service.CformTypePermissionService;
import org.springframework.stereotype.Service;


/**
 * Service Implementation for managing CformTypePermission.
 *
 * <AUTHOR>
 *
 */
@Service
public class CformTypePermissionServiceImpl implements CformTypePermissionService {
    @Override
    public CformTypePermission insert(CformTypePermission cformTypePermission) throws Exception {
        return null;
    }

    @Override
    public CformTypePermission update(CformTypePermission cformTypePermission) throws Exception {
        return null;
    }

    @Override
    public CformTypePermission getCfromType(String id) throws Exception {
        return null;
    }

    @Override
    public void delete(String id) throws Exception {

    }
}
