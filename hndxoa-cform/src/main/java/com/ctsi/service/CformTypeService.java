package com.ctsi.service;


import com.ctsi.common.bo.PageForm;
import com.ctsi.domain.CformType;
import com.ctsi.domain.CformTypeTree;
import com.ctsi.hndx.activitcform.PageCform;

import java.util.List;

public interface CformTypeService {
    void insert(CformType cfromType) throws Exception;

    void update(CformType cfromType) throws  Exception;

    CformType getCfromType(String id) throws Exception;

    void delete(String id) throws Exception;

    PageCform<CformType> getCformTypesListForPage(PageForm<CformType> form) throws Exception;

    List<CformTypeTree> getCformTypesTreeList(String parentId) throws Exception;
}
