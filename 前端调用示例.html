<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人社厅人员数据导出</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>人社厅人员数据导出功能</h1>
    
    <div class="container">
        <h2>功能说明</h2>
        <p>此功能将查询所有人社厅人员数据，按单位名称（strUnitName）分组，为每个单位生成独立的Excel文件，并打包成ZIP压缩包下载。</p>
        
        <h3>导出内容</h3>
        <ul>
            <li>姓名 (strUserId)</li>
            <li>身份证号 (idCardNo)</li>
            <li>手机号 (strMobile)</li>
            <li>单位名称 (strUnitName)</li>
            <li>单位统一社会信用码 (creditCode)</li>
        </ul>
    </div>

    <div class="container">
        <h2>导出操作</h2>
        <button id="exportBtn" class="button" onclick="exportData()">导出人社厅人员数据</button>
        <div id="status"></div>
    </div>

    <div class="container">
        <h2>JavaScript调用示例</h2>
        <div class="code-block">
            <pre><code>// 方法1: 使用fetch API
async function exportData() {
    try {
        const response = await fetch('/api/bizHrsUserInfo/exportAllUsersByUnit', {
            method: 'GET',
            headers: {
                'Accept': 'application/octet-stream'
            }
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '人社厅人员数据.zip';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            throw new Error('导出失败');
        }
    } catch (error) {
        console.error('导出错误:', error);
        alert('导出失败: ' + error.message);
    }
}

// 方法2: 使用window.open (简单方式)
function exportDataSimple() {
    window.open('/api/bizHrsUserInfo/exportAllUsersByUnit', '_blank');
}</code></pre>
        </div>
    </div>

    <div class="container">
        <h2>jQuery调用示例</h2>
        <div class="code-block">
            <pre><code>// 使用jQuery
function exportDataWithJQuery() {
    $.ajax({
        url: '/api/bizHrsUserInfo/exportAllUsersByUnit',
        method: 'GET',
        xhrFields: {
            responseType: 'blob'
        },
        success: function(data, status, xhr) {
            var blob = new Blob([data], { type: 'application/zip' });
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = '人社厅人员数据.zip';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        },
        error: function(xhr, status, error) {
            alert('导出失败: ' + error);
        }
    });
}</code></pre>
        </div>
    </div>

    <div class="container">
        <h2>cURL命令示例</h2>
        <div class="code-block">
            <pre><code># 使用cURL下载
curl -X GET "http://localhost:8080/api/bizHrsUserInfo/exportAllUsersByUnit" \
     -H "Accept: application/octet-stream" \
     --output "人社厅人员数据.zip"</code></pre>
        </div>
    </div>

    <script>
        async function exportData() {
            const button = document.getElementById('exportBtn');
            const status = document.getElementById('status');
            
            // 禁用按钮并显示加载状态
            button.disabled = true;
            button.textContent = '导出中...';
            status.innerHTML = '<div class="status info">正在导出数据，请稍候...</div>';
            
            try {
                const response = await fetch('/api/bizHrsUserInfo/exportAllUsersByUnit', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/octet-stream'
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '人社厅人员数据_' + new Date().getTime() + '.zip';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    status.innerHTML = '<div class="status success">导出成功！文件已开始下载。</div>';
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                console.error('导出错误:', error);
                status.innerHTML = '<div class="status error">导出失败: ' + error.message + '</div>';
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = '导出人社厅人员数据';
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('人社厅人员数据导出页面已加载');
        });
    </script>
</body>
</html>
