package com.ctsi.hndx.utils;

import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @desc IP操作工具类
 * 
 * <AUTHOR>
 * @since 6/20/2017 16:37 PM
 */
@Slf4j
public class IpUtil {
	private static final String IP_ADDRESS = "^([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}$";

	private static final String IP_PATTERN = "^(?:(?:[01]?\\d{1,2}|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d{1,2}|2[0-4]\\d|25[0-5])\\b";

	private static final String UNKNOWN = "unknown";

	private static final String LOOPBACK_ADDRESS = "127.0.0.1";

	private static final String UNKNOWN_ADDRESS = "0:0:0:0:0:0:0:1";

	/**
	 * @Description: 获取请求中的ip地址：过了多级反向代理，获取的ip不是唯一的，二是包含中间代理层ip。
	 * 
	 * @return 可能有多个，例如：*************， *************
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ip = "127.0.0.1";
		if (request != null) {
			ip = request.getHeader("x-forwarded-for");
			if (StringUtil.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
				ip = request.getHeader("Proxy-Client-IP");
			}

			if (StringUtil.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
				ip = request.getHeader("WL-Proxy-Client-IP");
			}

			if (StringUtil.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
				ip = request.getRemoteAddr();
			}
		}
		return ip;

	}

	/**
	 * 
	 * @Description: 获取客户端请求中的真实的ip地址
	 * 
	 * 获取客户端的IP地址的方法是：request.getRemoteAddr()，这种方法在大部分情况下都是有效的。
	 * 但是在通过了Apache，Squid等反向代理软件就不能获取到客户端的真实IP地址。而且，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，
	 * 而是一串ip值，例如：*************， *************， *************， *************。其中第一个*************才是用户真实的ip
	 */
	public static String getRealIp(HttpServletRequest request) {
		String ip = LOOPBACK_ADDRESS;
		if (request == null) {
			return ip;
		}

		ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
			if (LOOPBACK_ADDRESS.equals(ip) || UNKNOWN_ADDRESS.equals(ip)) {
				//根据网卡取本机配置的IP  
				try {
					InetAddress inet = InetAddress.getLocalHost();
					ip = inet.getHostAddress();
				} catch (UnknownHostException e) {
					log.error("getRealIp occurs error, caused by: ", e);
				}
			}
		}

		//"***.***.***.***".length() = 15
		int ipLength = 15;
		String separator = ",";
		if (ip != null && ip.length() > ipLength) {
			if (ip.indexOf(separator) > 0) {
				ip = ip.substring(0, ip.indexOf(","));
			}
		}
		return ip;
	}

	public static String getServiceIp() {
		Enumeration<NetworkInterface> netInterfaces = null;
		String ipsStr = "";

		try {
			netInterfaces = NetworkInterface.getNetworkInterfaces();
			while (netInterfaces.hasMoreElements()) {
				NetworkInterface ni = netInterfaces.nextElement();
				Enumeration<InetAddress> ips = ni.getInetAddresses();
				Pattern pattern = Pattern.compile(IP_PATTERN);
				while (ips.hasMoreElements()) {
					String ip = ips.nextElement().getHostAddress();
					Matcher matcher = pattern.matcher(ip);
					if (matcher.matches() && !"127.0.0.1".equals(ip)) {
						ipsStr = ip;
					}
				}
			}
		} catch (Exception e) {
			log.error("getServiceIp occurs error, caused by: ", e);
		}

		return ipsStr;
	}
	/**
	 * @description	判断是否为正确的ip地址
	 * @param ipName	IP地址
	 * @return 是ip地址，返回true，否，返回false
	 * @since 1.0
	 * */
	public static boolean isIpAddress(String ipName){
		return 	Pattern.matches(IpUtil.IP_ADDRESS, ipName);
	}
	public static void swap(String i, String j) {
		String temp = i;
		i = j;
		j = temp;
	}

	public static String getLocalIp() {
		String ipAddress = null;
		try {
			Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
			while (interfaces.hasMoreElements()) {
				NetworkInterface networkInterface = interfaces.nextElement();
				Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
				while (addresses.hasMoreElements()) {
					InetAddress address = addresses.nextElement();
					if (address instanceof Inet4Address) {
						int code = address.hashCode();
						if (!isLoopbackAddress(code) && !isLinkLocalAddress(code) && isSiteLocalAddress(code)) {
							ipAddress = address.getHostAddress();
							break;
						}
					}

				}
			}
		} catch (SocketException e) {
			log.error("获取本地ip地址失败 [{}]", e);
		}
		return ipAddress;
	}

	private static boolean isLoopbackAddress(int address) {
		/* 127.x.x.x */
		byte[] byteAddr = getAddress(address);
		return byteAddr[0] == 127;
	}

	private static boolean isLinkLocalAddress(int address) {
		// link-local unicast in IPv4 (***********/16)
		// defined in "Documenting Special Use IPv4 Address Blocks
		// that have been Registered with IANA" by Bill Manning
		// draft-manning-dsua-06.txt
		return (((address >>> 24) & 0xFF) == 169)
				&& (((address >>> 16) & 0xFF) == 254);
	}

	private static boolean isSiteLocalAddress(int address) {
		// refer to RFC 1918
		// 10/8 prefix
		// 172.16/12 prefix
		// 192.168/16 prefix
		return ((((address >>> 24) & 0xFF) == 172)
				&& (((address >>> 16) & 0xF0) == 16))
				|| ((((address >>> 24) & 0xFF) == 192)
				&& (((address >>> 16) & 0xFF) == 168))
				|| ((((address >>> 24) & 0xFF) == 134)
				&& (((address >>> 16) & 0xFF) == 160));
	}

	private static byte[] getAddress(int address) {

		byte[] addr = new byte[4];

		addr[0] = (byte) ((address >>> 24) & 0xFF);
		addr[1] = (byte) ((address >>> 16) & 0xFF);
		addr[2] = (byte) ((address >>> 8) & 0xFF);
		addr[3] = (byte) (address & 0xFF);
		return addr;
	}

}
